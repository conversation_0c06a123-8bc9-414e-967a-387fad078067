"""
Database Manager for SCTX Application
Handles database connections, sessions, and basic operations
"""
import logging
from contextlib import contextmanager
from typing import Optional, Type, Any, List, Dict
from sqlalchemy import create_engine, MetaData, Table
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, NoSuchTableError
import sqlalchemy

from models.models import Base, Login

logger = logging.getLogger(__name__)


class DatabaseManager:
    """Manages database connections and sessions"""
    
    def __init__(self, database_url: str = "sqlite:///data/sctx.db", echo: bool = False):
        """
        Initialize database manager
        
        Args:
            database_url: Database connection URL
            echo: Whether to echo SQL statements
        """
        self.database_url = database_url
        self.echo = echo
        self._engine = None
        self._session_factory = None
        self._initialize_database()
    
    def _initialize_database(self) -> None:
        """Initialize database engine and session factory"""
        try:
            self._engine = create_engine(self.database_url, echo=self.echo)
            self._session_factory = sessionmaker(bind=self._engine)
            
            # Create tables if they don't exist
            if not self._table_exists('commercent'):
                Base.metadata.create_all(self._engine)
                self._create_default_user()
                logger.info("Database tables created successfully")
            else:
                logger.info("Database already exists")
                
        except SQLAlchemyError as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _table_exists(self, table_name: str) -> bool:
        """Check if a table exists in the database"""
        try:
            metadata = MetaData()
            Table(table_name, metadata, autoload_with=self._engine)
            return True
        except NoSuchTableError:
            return False
    
    def _create_default_user(self) -> None:
        """Create default login user"""
        try:
            with self.get_session() as session:
                default_user = Login(name='sctx', password='dcw08')
                session.add(default_user)
                session.commit()
                logger.info("Default user created")
        except SQLAlchemyError as e:
            logger.error(f"Failed to create default user: {e}")
    
    @contextmanager
    def get_session(self) -> Session:
        """
        Get a database session with automatic cleanup
        
        Yields:
            Session: SQLAlchemy session
        """
        session = self._session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_factory(self) -> sessionmaker:
        """Get the session factory for manual session management"""
        return self._session_factory
    
    def close(self) -> None:
        """Close database connections"""
        if self._engine:
            self._engine.dispose()
            logger.info("Database connections closed")
    
    def execute_query(self, query: str, params: Optional[Dict[str, Any]] = None) -> List[Any]:
        """
        Execute a raw SQL query
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of query results
        """
        try:
            with self.get_session() as session:
                result = session.execute(query, params or {})
                return result.fetchall()
        except SQLAlchemyError as e:
            logger.error(f"Query execution failed: {e}")
            raise
    
    def backup_database(self, backup_path: str) -> bool:
        """
        Create a backup of the database
        
        Args:
            backup_path: Path for the backup file
            
        Returns:
            True if backup successful, False otherwise
        """
        try:
            # For SQLite, we can simply copy the file
            import shutil
            if self.database_url.startswith('sqlite:///'):
                source_path = self.database_url.replace('sqlite:///', '')
                shutil.copy2(source_path, backup_path)
                logger.info(f"Database backed up to {backup_path}")
                return True
            else:
                logger.warning("Backup not implemented for non-SQLite databases")
                return False
        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            return False
    
    def get_table_count(self, table_name: str) -> int:
        """
        Get the number of records in a table

        Args:
            table_name: Name of the table

        Returns:
            Number of records
        """
        try:
            with self.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                return result.scalar()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get table count for {table_name}: {e}")
            return 0


# Global database manager instance
db_manager: Optional[DatabaseManager] = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance"""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager


def initialize_database(database_url: str = "sqlite:///data/sctx.db") -> DatabaseManager:
    """Initialize the global database manager"""
    global db_manager
    db_manager = DatabaseManager(database_url)
    return db_manager


def close_database() -> None:
    """Close the global database manager"""
    global db_manager
    if db_manager:
        db_manager.close()
        db_manager = None

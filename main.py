################################################################################
##
## BY: boumares abdelhafid
## PROJECT MADE WITH: Qt Designer and PySide6
## V: 2.0.0 - Refactored for better performance and maintainability
##
################################################################################
import subprocess
import datetime
import re
import sys
import logging
from typing import Optional, Dict, Any, List
from dataclasses import dataclass

from PySide6.QtCore import Slot, QDateTime, QTimer, Signal
from PySide6.QtWidgets import QFileDialog
from PySide6 import QtCore
from PySide6.QtGui import QColor, QScreen
from PySide6.QtWidgets import *
from dateutil import parser

# Local imports
from database.database_manager import DatabaseManager
from models.models import Commercent, Activite, Infraction, Pv, Avertisment
from services.data_service import DataService
from ui.components.activity_widget import ActivityWidget
from ui.components.infraction_widget import InfractionWidget
from ui_sctx import Ui_MainWindow
from ui_splash_screen import Ui_SplashScreen
from utils.validators import InputValidator
from utils.logger import setup_logger

# Setup logging
logger = setup_logger(__name__)

@dataclass
class AppConfig:
    """Application configuration"""
    database_path: str = "data/sctx.db"
    window_title: str = "SCTX Management System"
    splash_duration: int = 3000
    auto_save_interval: int = 300000  # 5 minutes


# APPLICATION CLASSES

class ActivityManager(QFrame):
    """Standalone activity management window"""

    def __init__(self, data_service: DataService, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI for activity management"""
        layout = QVBoxLayout(self)

        # Import the activity widget
        from ui.components.activity_widget import ActivityWidget
        self.activity_widget = ActivityWidget(self.data_service)
        layout.addWidget(self.activity_widget)

        self.setWindowTitle("Activity Management")
        self.resize(800, 600)


class InfractionManager(QMainWindow):
    """Standalone infraction management window"""

    def __init__(self, data_service: DataService, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.setup_ui()

    def setup_ui(self):
        """Setup the UI for infraction management"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # Import the infraction widget
        from ui.components.infraction_widget import InfractionWidget
        self.infraction_widget = InfractionWidget(self.data_service)
        layout.addWidget(self.infraction_widget)

        self.setWindowTitle("Infraction Management")
        self.resize(800, 600)


class LegacyMainapp(QFrame):
    def __init__(self) -> None:
        QFrame.__init__(self)
        self.ui = MainUi()
        self.ui.setupUi(self)

        self.pushButton_4 = self.ui.pushButton_4
        self.pushButton = self.ui.pushButton
        self.pushButton_2 = self.ui.pushButton_2
        self.pushButton_3 = self.ui.pushButton_3
        self.tableWidget = self.ui.tableWidget
        self.verticalLayout = self.ui.verticalLayout
        self.label_3 = self.ui.label_3
        self.lineEdit_3 = self.ui.lineEdit_3
        self.horizontalLayout_4 = self.ui.horizontalLayout_4
        self.label_2 = self.ui.label_2
        self.lineEdit_2 = self.ui.lineEdit_2
        self.horizontalLayout_2 = self.ui.horizontalLayout_2
        self.horizontalLayout = self.ui.verticalLayout
        self.verticalLayout_2 = self.ui.verticalLayout_2
        # -------------Slot-----------------------------------
        self.loaddata()
        self.tableWidget.currentCellChanged.connect(self._cellclicked)
        self.lineEdit_2.returnPressed.connect(self.lineEdit_2.clear)
        self.pushButton_3.clicked.connect(self._ondelete)
        self.pushButton.clicked.connect(self._add)
        self.pushButton_2.clicked.connect(self._mod)
        self.pushButton_4.clicked.connect(self._save)
        self.lineEdit_3.textChanged.connect(self.serche)
        # ----------------------function----------------------------

    def loaddata(self):
        acts = data.session.query(
            data.Activite_Tab).order_by(data.Activite_Tab.id)

        self.tableWidget.setRowCount(acts.count())

        tableRow = 0
        for row in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row.Nom_act))

            tableRow += 1

    def _cellclicked(self):
        global id
        r = self.tableWidget.currentRow()
        c = self.tableWidget.columnCount()
        cell = []
        for i in range(c):
            it = self.tableWidget.item(r, i)
            itm = QTableWidgetItem(it).text()
            cell.append(itm)
        id = cell[0]
        self.lineEdit_2.setText(cell[1])

    def mssage(self, title, text):
        reply = QMessageBox.question(self, title, text,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        return reply

    def _add(self):
        name = self.lineEdit_2.text()

        if name == '':
            if self.mssage('استعلام', 'لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                self.lineEdit_2.setFocus()

        else:
            data.act(name)
            self.loaddata()
            self.lineEdit_2.clear()
            self.lineEdit_2.setFocus()

    def _mod(self):

        name = self.lineEdit_2.text()
        if name == '':
            if self.mssage('استعلام', 'قم باختيار في الجدول ') == QMessageBox.Yes:
                self.lineEdit_2.setFocus()

        else:
            if self.mssage('Confirmation', 'هل تريد التعديل فعلا') == QMessageBox.Yes:
                data.mod_act(id, name)
                self.lineEdit_2.clear()
                self.loaddata()

    def serche(self):

        sh = self.lineEdit_3.text()

        acts = data.session.query(data.Activite_Tab).filter(
            data.Activite_Tab.Nom_act.ilike('%' + sh + '%'))
        self.tableWidget.setRowCount(acts.count())

        tableRow = 0
        for row in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row.Nom_act))

            tableRow += 1
        if sh == "":
            self.loaddata()

    def _ondelete(self):
        if self.lineEdit_2.text() == '':
            if self.mssage('استعلام', 'قم باختيار في الجدول ') == QMessageBox.Yes:
                self.lineEdit_2.setFocus()

        else:
            if self.mssage('Confirmation', 'هل تريد الحذف فعلا') == QMessageBox.Yes:
                data.sup_act(id)
                self.loaddata()

    def _save(self):
        if self.mssage('Confirmation', 'هل تريد حفظ في قاعدة البيانات ') == QMessageBox.Yes:
            data.session.commit()
            self.loaddata()
            self.lineEdit_2.clear()

    def styleSheet(self):
        return """
        QFrame{
        background-color:  rgb(221, 217, 255);
        }
        QLineEdit{
             background: #111;
            border-radius: 5px;
            background: #111;
            color : #fff; 
            font-family: system-ui;
            font-size: 20px;
        }

           QPushButton{
            border-style:outset;
            border-with : 2px;
            border-radius:10px;
            border-color: beige;
            font:bold 14px;
            min-with : 10 em;
            padding : 6px;
            transition: 0.5s;
            }
            QPushButton::hover{
            background-color:#1aff6e;
            letter-spacing: 1px;
            }

        """


class Maininf(QMainWindow, ui_infraction):
    def __init__(self) -> None:
        QMainWindow.__init__(self)
        self.setupUi(self)

        self.pushButton_3 = self.pushButton_3
        self.pushButton_4 = self.pushButton_4
        self.pushButton = self.pushButton
        self.pushButton_2 = self.pushButton_2
        self.lineEdit_2 = self.lineEdit_2
        self.lineEdit = self.lineEdit
        self.tableWidget = self.tableWidget
        self.verticalLayout = self.verticalLayout
        self.centralwidget = self.centralwidget

        self.loaddata()
        self.lineEdit.returnPressed.connect(self.lineEdit.clear)
        self.lineEdit_2.returnPressed.connect(self.lineEdit_2.clear)
        self.tableWidget.currentCellChanged.connect(self._cellclicked)
        self.pushButton_2.clicked.connect(self._add)
        self.pushButton.clicked.connect(self._mod)
        self.pushButton_3.clicked.connect(self._ondelete)
        self.pushButton_4.clicked.connect(self._save)
        self.lineEdit_2.textChanged.connect(self.serche)

    def message(self, text):
        msg = QMessageBox.question(self, MainWindow.windowTitle(self), text,
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        return msg

    def loaddata(self):

        acts = data.session.query(
            data.Infraction).order_by(data.Infraction.id)
        self.tableWidget.setRowCount(acts.count())
        tableRow = 0
        for row in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row.Nom_inf))

            tableRow += 1

    @Slot()
    def _cellclicked(self):
        global id
        r = self.tableWidget.currentRow()
        c = self.tableWidget.columnCount()
        cell = []
        for i in range(c):
            it = self.tableWidget.item(r, i)
            itm = QTableWidgetItem(it).text()
            cell.append(itm)
        id = cell[0]
        self.lineEdit.setText(cell[1])

    def _add(self):
        name = self.lineEdit.text()
        if name == '':

            if self.message('لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                self.lineEdit.setFocus()
        else:
            data.add_Inf(name)
            self.loaddata()
            self.lineEdit.clear()
            self.lineEdit.setFocus()

    def _mod(self):
        name = self.lineEdit.text()
        if name == '':

            if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                self.lineEdit.setFocus()
        else:

            if self.message('هل تريد التعديل فعلا') == QMessageBox.Yes:
                data.update_Inf(id, name)
                self.lineEdit.clear()
                self.loaddata()

    def _ondelete(self):
        if self.message('هل تريد الحذف فعلا') == QMessageBox.Yes:
            if id != '':
                data.delete_Inf(id)
                self.loaddata()
                self.tableWidget.show
            else:
                self.message('لا يوجد مخالفات لحذفها')

    def _save(self):

        if self.message('هل تريد حفظ في قاعدة البيانات ') == QMessageBox.Yes:
            data.session.commit()

    def serche(self):
        sh = self.lineEdit_2.text()
        acts = data.session.query(data.Infraction).filter(
            data.Infraction.Nom_inf.ilike('%' + sh + '%'))
        self.tableWidget.setRowCount(acts.count())
        tableRow = 0
        for row_ in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row_.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row_.Nom_inf))
            tableRow += 1
        if sh == "":
            self.loaddata()


class MainWindow(QMainWindow):
    """Refactored main window with improved architecture"""

    def __init__(self, config: AppConfig) -> None:
        super().__init__()
        self.config = config
        self.logger = logger

        # Initialize services
        self.db_manager = DatabaseManager(config.database_path)
        self.data_service = DataService(self.db_manager)

        # UI setup
        self.ui = Ui_MainWindow()
        self.ui.setupUi(self)
        self.setWindowTitle(config.window_title)

        # State management
        self._submit_counter = 0
        self.current_commercent_id: Optional[int] = None
        self.current_activity_id: Optional[int] = None
        self.current_pv_id: Optional[int] = None
        self.current_avertissement_id: Optional[int] = None

        # Child windows
        self.activity_manager: Optional[ActivityManager] = None
        self.infraction_manager: Optional[InfractionManager] = None

        # Auto-save timer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.timeout.connect(self.auto_save)
        self.auto_save_timer.start(config.auto_save_interval)
        # Initialize UI components
        self.setup_ui_references()
        self.setup_initial_data()
        self.setup_connections()

        self.logger.info("Main window initialized successfully")

    def setup_ui_references(self):
        """Setup references to UI components for easier access"""
        # Main UI components
        self.tabWidget = self.ui.tabWidget
        self.tabWidget_2 = self.ui.tabWidget_2

        # Commercial entity components
        self.tableWidget_com = self.ui.tableWidget_com
        self.reche_com = self.ui.reche_com
        self.combox_com = self.ui.combox_com
        self.reg_com = self.ui.reg_com
        self.nom_com = self.ui.nom_com
        self.prenom_com = self.ui.prenom_com
        self.date_nai_com = self.ui.date_nai_com
        self.lieu_nai_com = self.ui.lieu_nai_com
        self.prenomPer_com = self.ui.prenomPer_com
        self.nomMe_com = self.ui.nomMe_com
        self.prenomMer_com = self.ui.prenomMer_com
        self.Adresse_com = self.ui.Adresse_com
        self.adresseLoc_com = self.ui.adresseLoc_com
        self.dateReg_com = self.ui.dateReg_com
        self.setuation_com = self.ui.setuation_com

        # Activity components
        self.tableWidget_act = self.ui.tableWidget_act
        self.code_actevite = self.ui.code_actevite
        self.comboBox_act = self.ui.comboBox_act
        self.Button_actuelise = self.ui.Button_actuelise

        # PV components
        self.tableWidget_pv = self.ui.tableWidget_pv
        self.numero_reg = self.ui.numero_reg
        self.numero_pv = self.ui.numero_pv
        self.date_pv = self.ui.date_pv
        self.date_const = self.ui.date_const
        self.date_envoi_jus = self.ui.date_envoi_jus
        self.date_audience = self.ui.date_audience
        self.annee = self.ui.annee
        self.infraction_pv = self.ui.infraction_pv
        self.btn_pv = self.ui.btn_pv

        # Avertissement components
        self.tableWidget_avert = self.ui.tableWidget_avert
        self.numero_recue = self.ui.numero_recue
        self.date_avert = self.ui.date_avert
        self.date_recue = self.ui.date_recue
        self.montant_recue = self.ui.montant_recue

        # Status displays
        self.lcdNumber = self.ui.lcdNumber
        self.lcdNumber_2 = self.ui.lcdNumber_2

        # Setup LCD display
        self.lcdNumber.setDigitCount(8)
        current_datetime = QDateTime.currentDateTime()
        date_str = current_datetime.toString("yyyy-MM-dd")
        self.lcdNumber.display(date_str)

    def setup_initial_data(self):
        """Load initial data"""
        try:
            self.load_commercents()
            self.populate_activity_combo()
            self.populate_infraction_combo()
            self.populate_year_combo()
        except Exception as e:
            self.logger.error(f"Failed to load initial data: {e}")
            self.show_error("Initialization Error", f"Failed to load initial data: {e}")

    def setup_connections(self):
        """Setup signal connections"""
        # Tab navigation
        self.tabWidget_2.currentChanged.connect(self.on_tab_changed)
        self.tabWidget.currentChanged.connect(self.on_main_tab_changed)

        # Table selections
        self.tableWidget_com.currentCellChanged.connect(self.on_commercent_selected)
        self.tableWidget_act.currentCellChanged.connect(self.on_activity_selected)
        self.tableWidget_pv.currentCellChanged.connect(self.on_pv_selected)
        self.tableWidget_avert.currentCellChanged.connect(self.on_avertissement_selected)

        # Actions
        self.ui.actionNouveau.triggered.connect(self.new_record)
        self.ui.actionAjouter.triggered.connect(self.add_record)
        self.ui.actionModifier.triggered.connect(self.update_record)
        self.ui.actionSuprimer.triggered.connect(self.delete_record)
        self.ui.actionSave.triggered.connect(self.save_changes)
        self.ui.actionClose.triggered.connect(self.close_application)

        # Search
        self.reche_com.textChanged.connect(self.search_commercents)

        # Navigation
        self.ui.actionNext.triggered.connect(self.next_record)
        self.ui.actionReturn.triggered.connect(self.previous_record)

        # Window management
        self.ui.actionShow.triggered.connect(self.tabWidget_2.show)
        self.ui.actionHide.triggered.connect(self.tabWidget_2.hide)

        # Table management
        self.ui.actiontable_Activite.triggered.connect(self.show_activity_manager)
        self.ui.actiontable_Infraction.triggered.connect(self.show_infraction_manager)

        # Refresh buttons
        self.Button_actuelise.clicked.connect(self.populate_activity_combo)
        self.btn_pv.clicked.connect(self.populate_infraction_combo)

        # Printing
        self.ui.actionImprimer.triggered.connect(self.print_report)

    # Core data loading methods
    def load_commercents(self, limit: Optional[int] = None):
        """Load commercial entities into the table"""
        try:
            commercents = self.data_service.get_all_commercents(limit=limit)
            self.populate_commercent_table(commercents)

            # Update count display
            count = len(commercents)
            self.lcdNumber_2.setDigitCount(len(str(count)))
            self.lcdNumber_2.display(count)

            self.logger.info(f"Loaded {count} commercial entities")

        except Exception as e:
            self.logger.error(f"Failed to load commercial entities: {e}")
            self.show_error("Data Loading Error", f"Failed to load commercial entities: {e}")

    def populate_commercent_table(self, commercents: List):
        """Populate the commercial entities table"""
        self.tableWidget_com.setRowCount(len(commercents))

        for row, commercent in enumerate(commercents):
            self.tableWidget_com.setItem(row, 0, QTableWidgetItem(str(commercent.id)))
            self.tableWidget_com.setItem(row, 1, QTableWidgetItem(commercent.Num_reg or ""))
            self.tableWidget_com.setItem(row, 2, QTableWidgetItem(commercent.Nom_com or ""))
            self.tableWidget_com.setItem(row, 3, QTableWidgetItem(commercent.Prenom_com or ""))
            self.tableWidget_com.setItem(row, 4, QTableWidgetItem(str(commercent.Date_nai) if commercent.Date_nai else ""))
            self.tableWidget_com.setItem(row, 5, QTableWidgetItem(commercent.Lieu_nai or ""))
            self.tableWidget_com.setItem(row, 6, QTableWidgetItem(commercent.Prenom_per or ""))
            self.tableWidget_com.setItem(row, 7, QTableWidgetItem(commercent.Nom_mer or ""))
            self.tableWidget_com.setItem(row, 8, QTableWidgetItem(commercent.Prenom_mer or ""))
            self.tableWidget_com.setItem(row, 9, QTableWidgetItem(commercent.Adresse_dom or ""))
            self.tableWidget_com.setItem(row, 10, QTableWidgetItem(commercent.Adresse_local or ""))
            self.tableWidget_com.setItem(row, 11, QTableWidgetItem(str(commercent.Date_reg) if commercent.Date_reg else ""))
            self.tableWidget_com.setItem(row, 12, QTableWidgetItem(commercent.Etat or ""))

    def populate_activity_combo(self):
        """Populate activity combo box with templates"""
        try:
            self.comboBox_act.clear()
            templates = self.data_service.get_all_activity_templates()
            for template in templates:
                self.comboBox_act.addItem(template.Nom_act)
            self.logger.info(f"Populated activity combo with {len(templates)} items")
        except Exception as e:
            self.logger.error(f"Failed to populate activity combo: {e}")

    def populate_infraction_combo(self):
        """Populate infraction combo box"""
        try:
            self.infraction_pv.clear()
            infractions = self.data_service.get_all_infractions()
            for infraction in infractions:
                self.infraction_pv.addItem(infraction.Nom_inf)
            self.logger.info(f"Populated infraction combo with {len(infractions)} items")
        except Exception as e:
            self.logger.error(f"Failed to populate infraction combo: {e}")

    def populate_year_combo(self):
        """Populate year combo box"""
        current_year = datetime.datetime.now().year
        for year in range(2023, current_year + 18):
            self.annee.addItem(str(year))

    # Message dialogs
    def show_question(self, title: str, message: str) -> bool:
        """Show question dialog and return True if Yes clicked"""
        reply = QMessageBox.question(
            self, title, message,
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        return reply == QMessageBox.Yes

    def show_error(self, title: str, message: str):
        """Show error message"""
        QMessageBox.critical(self, title, message)

    def show_warning(self, title: str, message: str):
        """Show warning message"""
        QMessageBox.warning(self, title, message)

    def show_info(self, title: str, message: str):
        """Show info message"""
        QMessageBox.information(self, title, message)

    def message(self, text):
        """Legacy message method for compatibility"""
        return self.show_question("Confirmation", text)

    def setCur_index(self):
        if self.tabWidget_2.currentIndex() == 0:
            self.tabWidget.setCurrentIndex(1)
        elif self.tabWidget_2.currentIndex() == 1:
            self.tabWidget.setCurrentIndex(2)
        elif self.tabWidget_2.currentIndex() == 2:
            self.tabWidget.setCurrentIndex(3)
        elif self.tabWidget_2.currentIndex() == 3:
            self.tabWidget.setCurrentIndex(4)
        else:
            pass

    def setCur_tab(self):
        if self.tabWidget.currentIndex() == 1:
            self.tabWidget_2.setCurrentIndex(0)
        elif self.tabWidget.currentIndex() == 2:
            self.tabWidget_2.setCurrentIndex(1)
        elif self.tabWidget.currentIndex() == 3:
            self.tabWidget_2.setCurrentIndex(2)
        elif self.tabWidget.currentIndex() == 4:
            self.tabWidget_2.setCurrentIndex(3)
        else:
            pass

    def show_table_activite(self):
        self.main = Mainapp()
        self.main.setStyleSheet(self.main.styleSheet())
        center = QScreen.availableGeometry(QApplication.primaryScreen()).center()
        geo = self.main.frameGeometry()
        geo.moveCenter(center)
        if self.main.isVisible():
            self.main.hide()
        else:
            self.main.show()

    def show_table_infraction(self):
        self.main = Maininf()
        center = QScreen.availableGeometry(QApplication.primaryScreen()).center()
        geo = self.main.frameGeometry()
        geo.moveCenter(center)
        if self.main.isVisible():
            self.main.hide()
        else:
            self.main.show()

    def add_year(self):
        for row in range(2023,2041):
            self.annee.addItem(str(row))

    def add_items(self):
        acts = data.session.query(
            data.Activite_Tab).order_by(data.Activite_Tab.id)
        for row in acts:
            self.comboBox_act.addItem(row.Nom_act)

    def add_items_inf(self):
        acts = data.session.query(
            data.Infraction).order_by(data.Infraction.id)
        for row in acts:
            self.infraction_pv.addItem(row.Nom_inf)

    def actualise_act(self):
        self.comboBox_act.clear()
        self.add_items()

    def actualise_inf(self):
        self.infraction_pv.clear()
        self.add_items_inf()

    def loaddata_com(self):
        acts = data.session.query(
            data.Commercent).order_by(data.Commercent.id)
        self.show_data(acts)
        self.lcdNumber_2.setDigitCount(acts.count())
        self.lcdNumber_2.display(str(acts.count()))

    def show_data(self, acts):
        self.tableWidget_com.setRowCount(acts.count())
        table_row = 0
        for row in acts:
            self.tableWidget_com.setItem(
                table_row, 0, QTableWidgetItem(str(row.id)))
            self.tableWidget_com.setItem(
                table_row, 1, QTableWidgetItem(row.Num_reg))
            self.tableWidget_com.setItem(
                table_row, 2, QTableWidgetItem(row.Nom_com))
            self.tableWidget_com.setItem(
                table_row, 3, QTableWidgetItem(row.Prenom_com))
            self.tableWidget_com.setItem(
                table_row, 4, QTableWidgetItem(str(row.Date_nai)))
            self.tableWidget_com.setItem(
                table_row, 5, QTableWidgetItem(row.Lieu_nai))
            self.tableWidget_com.setItem(
                table_row, 6, QTableWidgetItem(row.Prenom_per))
            self.tableWidget_com.setItem(
                table_row, 7, QTableWidgetItem(row.Nom_mer))
            self.tableWidget_com.setItem(
                table_row, 8, QTableWidgetItem(row.Prenom_mer))
            self.tableWidget_com.setItem(
                table_row, 9, QTableWidgetItem(row.Adresse_dom))
            self.tableWidget_com.setItem(
                table_row, 10, QTableWidgetItem(row.Adresse_local))
            self.tableWidget_com.setItem(
                table_row, 11, QTableWidgetItem(str(row.Date_reg)))
            self.tableWidget_com.setItem(
                table_row, 12, QTableWidgetItem(row.Etat))
            table_row += 1

    def cellclicked_(self, row, colum, items: QTableWidget.__name__):
        r = row
        c = colum
        cell = []
        for i in range(c):
            it = items(r, i)
            itm = QTableWidgetItem(it).text()
            cell.append(itm)
        return cell

    def set_date(self, list_date, date):
        if list_date != "None":
            date_ = list(list_date)
            date.setDate(
                datetime.date(
                    year=int(date_[0] + date_[1] + date_[2] + date_[3]),
                    month=int(date_[5] + date_[6]),
                    day=int(date_[8] + date_[9]),
                )
            )

    def cellclicked_com(self):
        global id
        cell = self.cellclicked_(
            self.tableWidget_com.currentRow(), self.tableWidget_com.columnCount(), self.tableWidget_com.item)
        if cell[0] != '':
            id = int(cell[0])
            self.reg_com.setText(cell[1])
            self.numero_reg.setText(cell[1])
            self.nom_com.setText(cell[2])
            self.prenom_com.setText(cell[3])
            self.set_date(cell[4], date=self.date_nai_com)
            self.lieu_nai_com.setText(cell[5])
            self.prenomPer_com.setText(cell[6])
            self.nomMe_com.setText(cell[7])
            self.prenomMer_com.setText(cell[8])
            self.Adresse_com.setText(cell[9])
            self.adresseLoc_com.setText(cell[10])
            self.set_date(cell[11], date=self.dateReg_com)
        self.afiche_act()
        self.afiche_pv()

    def afiche_act(self):
        try:
            acts = data.session.query(
                data.Activite).filter(data.Activite.parent_id == id)
            self.tableWidget_act.setRowCount(acts.count())
            table_row = 0
            for row in acts:
                self.tableWidget_act.setItem(
                    table_row, 0, QTableWidgetItem(str(row.id)))
                self.tableWidget_act.setItem(
                    table_row, 2, QTableWidgetItem(row.Nom_act))
                self.tableWidget_act.setItem(
                    table_row, 1, QTableWidgetItem(row.Code_act))
                table_row += 1
        except NameError as er:
            print(f"Erreur : {er}")

    # -------------------------------------------selction pv ---------

    def afiche_pv(self):
        try:
            acts = data.session.query(
                data.Pv).filter(data.Pv.parent_id == id)
            self.tableWidget_pv.setRowCount(acts.count())
            table_row = 0
            for row in acts:
                self.tableWidget_pv.setItem(
                    table_row, 0, QTableWidgetItem(str(row.id)))
                self.tableWidget_pv.setItem(
                    table_row, 1, QTableWidgetItem(str(row.nume_pv)))
                self.tableWidget_pv.setItem(
                    table_row, 2, QTableWidgetItem(str(row.date_pv)))
                self.tableWidget_pv.setItem(
                    table_row, 3, QTableWidgetItem(str(row.date_constatation)))
                self.tableWidget_pv.setItem(
                    table_row, 4, QTableWidgetItem(str(row.date_envoi_jus)))
                self.tableWidget_pv.setItem(
                    table_row, 7, QTableWidgetItem(str(row.date_odience)))
                self.tableWidget_pv.setItem(
                    table_row, 6, QTableWidgetItem(str(row.annee)))
                self.tableWidget_pv.setItem(
                    table_row, 5, QTableWidgetItem(str(row.infraction)))

                table_row += 1
        except NameError as er:
            print(f"Erreur : {er}")


            # -------------------------------------------------------selction actevite

    def cellclicked_act(self):
        global id_
        cell = self.cellclicked_(
            self.tableWidget_act.currentRow(), self.tableWidget_act.columnCount(), self.tableWidget_act.item)
        if cell[0] != '':
            id_ = int(cell[0])
            self.code_actevite.setText(cell[1])
            # self.nom_activete.setText(cell[2])

    # -----------------------------select tabl Pv
    def cellcliked_pv(self):
        global id_pv
        cell = self.cellclicked_(
            self.tableWidget_pv.currentRow(), self.tableWidget_pv.columnCount(), self.tableWidget_pv.item)
        if cell[0] != '':
            id_pv = int(cell[0])
            self.numero_pv.setText(cell[1])
            self.set_date(cell[2], date=self.date_pv)
            self.set_date(cell[3], date=self.date_const)
            self.set_date(cell[4], date=self.date_envoi_jus)
            self.set_date(cell[7], date=self.date_audience)
        self.afiche_avert()

    def cellcliked_avert(self):
        global  id_avert
        cell = self.cellclicked_(self.tableWidget_avert.currentRow(),self.tableWidget_avert.columnCount(),
                                 self.tableWidget_avert.item)
        if cell[0] != '':
            id_avert = int(cell[0])
            self.numero_recue.setText(cell[1])
            self.set_date(cell[2],date=self.date_avert)
            self.set_date(cell[3], date=self.date_recue)
            self.montant_recue.setText(cell[4])



    def afiche_avert(self):
        try:
            acts = data.session.query(
                data.Avertisment).filter(data.Avertisment.parent_id == id_pv)
            self.tableWidget_avert.setRowCount(acts.count())
            table_row = 0
            for row in acts:
                self.tableWidget_avert.setItem(table_row, 0, QTableWidgetItem(str(row.id)))
                self.tableWidget_avert.setItem(table_row, 1, QTableWidgetItem(str(row.nume_recu)))
                self.tableWidget_avert.setItem(table_row, 2, QTableWidgetItem(str(row.date_avert)))
                self.tableWidget_avert.setItem(table_row, 3, QTableWidgetItem(str(row.date_recu)))
                self.tableWidget_avert.setItem(table_row, 4, QTableWidgetItem(str(row.montant)))
                table_row += 1

        except NameError as er:
            print(f"Erreur : {er}")

    # --------------------------------------------------------------selection commercent

    def new_data(self):
        if self.tabWidget_2.currentIndex() == 0:
            self.reg_com.clear()
            self.nom_com.clear()
            self.prenom_com.clear()
            self.lieu_nai_com.clear()
            self.prenomPer_com.clear()
            self.nomMe_com.clear()
            self.prenomMer_com.clear()
            self.Adresse_com.clear()
            self.adresseLoc_com.clear()
        else:
            pass

    # ---------------------------------insertion commercent------------------
    def _add(self):

        if self.tabWidget_2.currentIndex() == 0:
            value = {
                1: self.reg_com.text(),
                2: self.nom_com.text(),
                3: self.prenom_com.text(),
                4: parser.parse(self.date_nai_com.text()),  # self.dateEdit.text(),
                5: self.lieu_nai_com.text(),
                6: self.prenomPer_com.text(),
                7: self.nomMe_com.text(),
                8: self.prenomMer_com.text(),
                9: self.Adresse_com.text(),
                10: self.adresseLoc_com.text(),
                11: parser.parse(self.dateReg_com.text()),  # self.dateEdit_2.text(),
                12: self.setuation_com.currentText()
            }
            if value[1] == '' or value[2] == '' or value[3] == '':
                if self.message('لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                    self.reg_com.setFocus()
            else:
                data.add_Com(value)
                self.new_data()
                self.loaddata_com()
        # --------------------------------------------------------- insertion Activite
        elif self.tabWidget_2.currentIndex() == 1:
            code = self.code_actevite.text()
            name = self.comboBox_act.currentText()
            try:
                if name == '':
                    if self.message('لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                        self.code_actevite.setFocus()
                else:
                    data.add_Act(code, name, id)
                    self.afiche_act()
                    self.code_actevite.clear()
            except:
                if self.message('اختر تاجر من الجدول اولا') == QMessageBox.Yes:
                    self.code_actevite.setFocus()
        # ---------------------------------------------------- insertion Pv

        elif self.tabWidget_2.currentIndex() == 2:
            value = [self.numero_pv.text(), parser.parse(self.date_pv.text()),
                     parser.parse(self.date_const.text()), parser.parse(self.date_envoi_jus.text()),
                     self.infraction_pv.currentText(), self.annee.currentText(), parser.parse(self.date_audience.text())]
            try:
                if value[0] == '':
                    if self.message('لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                        self.numero_pv.setFocus()
                else:
                    data.add_pv(value, id)
                    self.afiche_pv()
                    self.numero_pv.clear()
            except:
                if self.message('اختر تاجر من الجدول اولا') == QMessageBox.Yes:
                    self.numero_pv.setFocus()
            #-------------------------------------------insertion Avertisement
        elif self.tabWidget_2.currentIndex() == 3:
            float_num = float(self.montant_recue.text())
            value_3 = "{:.2f}".format(float_num)

            value = [self.numero_recue.text(),parser.parse(self.date_avert.text()),
                     parser.parse(self.date_recue.text()),value_3]
            try:
                if value[0] == '':
                    if self.message('لا يجب ان يكون الحقل فارغا') == QMessageBox.Yes:
                        self.numero_recue.setFocus()
                else:

                    data.add_avert(value,id_pv)
                    self.afiche_avert()
                    self.numero_recue.clear()
                    self.montant_recue.clear()
            except:
                if self.message('اختر محضر من الجدول اولا') == QMessageBox.Yes:
                    self.numero_recue.setFocus()
        else:
            pass

    def _mod(self):
        # ------------------------------------modif commercent
        if self.tabWidget_2.currentIndex() == 0:
            value = {
                1: self.reg_com.text(),
                2: self.nom_com.text(),
                3: self.prenom_com.text(),
                4: parser.parse(self.date_nai_com.text()),  # self.dateEdit.text(),
                5: self.lieu_nai_com.text(),
                6: self.prenomPer_com.text(),
                7: self.nomMe_com.text(),
                8: self.prenomMer_com.text(),
                9: self.Adresse_com.text(),
                10: self.adresseLoc_com.text(),
                11: parser.parse(self.dateReg_com.text()),  # self.dateEdit_2.text(),
                12: self.setuation_com.currentText()
            }
            if value[1] == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.reg_com.setFocus()
            else:
                if self.message('هل تريد التعديل فعلا') == QMessageBox.Yes:
                    data.update_Com(id, value)
                    self.new_data()
                    self.loaddata_com()
                    self.reg_com.setFocus()
        # ----------------------------------------------modif Activite
        elif self.tabWidget_2.currentIndex() == 1:

            code = self.code_actevite.text()
            name = self.comboBox_act.currentText()
            if name == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.code_actevite.setFocus()
            else:
                if self.message('هل تريد التعديل فعلا') == QMessageBox.Yes:
                    data.update_Act(id_, name, code)
                    self.code_actevite.clear()
                    self.afiche_act()
        # ------------------------------------------modif Pv
        elif self.tabWidget_2.currentIndex() == 2:
            value = [self.numero_pv.text(), parser.parse(self.date_pv.text()),
                     parser.parse(self.date_const.text()), parser.parse(self.date_envoi_jus.text()),
                     self.infraction_pv.currentText(), self.annee.currentText(), parser.parse(self.date_audience.text())]
            if value[0] == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.code_actevite.setFocus()
            else:
                if self.message('هل تريد التعديل فعلا') == QMessageBox.Yes:
                    data.mod_pv(id_pv, value)
                    self.numero_pv.clear()
                    self.afiche_pv()
            #------------------------------------------modif Avertisment
        elif self.tabWidget_2.currentIndex() == 3:
            float_num = float(self.montant_recue.text())
            value_3 = "{:.2f}".format(float_num)
            value = [self.numero_recue.text(),parser.parse(self.date_avert.text()),
                     parser.parse(self.date_recue.text()),value_3]

            if value[0] == '':
                if self.message('قم باختيار في الجدول') == QMessageBox.Yes:
                   self.numero_recue.setFocus()
            else:
                if self.message('هل تريد التعديل فعلا') == QMessageBox.Yes:
                    data.mod_avert(id_avert,value)
                    self.afiche_avert()
                    self.numero_recue.clear()
                    self.montant_recue.clear()

    def _del(self):
        # ---------------------------suprimr commercent
        if self.tabWidget_2.currentIndex() == 0:
            if self.reg_com.text() == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.reg_com.setFocus()
            else:
                if self.message('هل تريد الحذف فعلا') == QMessageBox.Yes:
                    data.delete_Com(id)
                    self.loaddata_com()
        # --------------------------------------suprime activite
        elif self.tabWidget_2.currentIndex() == 1:
            if id_ == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.nom_activete.setFocus()
            else:
                if self.message('هل تريد الحذف فعلا') == QMessageBox.Yes:
                    data.delete_Act(id_)
                    self.afiche_act()
        # -------------------------------------------suprimr Pv
        elif self.tabWidget_2.currentIndex() == 2:
            if self.numero_pv.text() == '':
                if self.message('قم باختيار في الجدول ') == QMessageBox.Yes:
                    self.numero_pv.setFocus()
            else:
                if self.message('هل تريد الحذف فعلا') == QMessageBox.Yes:
                    data.delete_pv(id_pv)
                    self.numero_reg.clear()
                    self.numero_pv.clear()
                    self.afiche_pv()

    def find_data(self):
        global acts
        sh = self.reche_com.text()
        ch1 = re.findall(r"(?=\D)\w", sh)
        sh2 = self.combox_com.currentIndex()
        # if sh2 == 0:
        #    acts = data.session.query(data.Commercent).filter(
        #        data.Commercent.Nom_com.ilike('%' + ''.join(str(e) for e in ch1) + '%'))
        # elif sh2 == 1:
        #    acts = data.session.query(data.Commercent).filter(
        #        data.Commercent.Prenom_com.ilike('%' + ''.join(str(e) for e in ch1) + '%'))
        # elif sh2 == 2:
        #    acts = data.session.query(data.Commercent).filter(
        #        data.Commercent.Num_reg.ilike('%' + sh + '%'))
        # else:
        #    self.loaddata_com()
        # self.show_data(acts)
        match sh2:
            case 0:

                acts = data.session.query(data.Commercent).filter(
                    data.Commercent.Nom_com.ilike('%' + ''.join(str(e) for e in ch1) + '%'))
            case 1:
                acts = data.session.query(data.Commercent).filter(
                    data.Commercent.Prenom_com.ilike('%' + ''.join(str(e) for e in ch1) + '%'))
            case 2:
                acts = data.session.query(data.Commercent).filter(
                    data.Commercent.Num_reg.ilike('%' + sh + '%'))
            case _:
                self.loaddata_com()
        self.show_data(acts)


    def set_text(self, index, row):
        if index == 0:
            for res in row:
                self.reg_com.setText(res[0].Num_reg)
                self.nom_com.setText(res[0].Nom_com)
                self.prenom_com.setText(res[0].Prenom_com)
                if res[0].Date_nai != "None":
                    self.date_nai_com.setDate(res[0].Date_nai)
                self.lieu_nai_com.setText(res[0].Lieu_nai)
                self.prenomPer_com.setText(res[0].Prenom_per)
                self.nomMe_com.setText(res[0].Nom_mer)
                self.prenomMer_com.setText(res[0].Prenom_mer)
                self.Adresse_com.setText(res[0].Adresse_dom)
                self.adresseLoc_com.setText(res[0].Adresse_local)
                if res[0].Date_reg != "None":
                    self.dateReg_com.setDate(res[0].Date_reg)
        elif index == 1:
            for res in row:
                self.code_actevite.setText(res[0].Code_act)
        else:
            pass

    def next_Tab(self):
        self._submit_counter += 1
        if self.tabWidget_2.currentIndex() == 0:
            acts = data.session.query(
                data.Commercent).order_by(data.Commercent.id)
            if self._submit_counter <= acts.count():
                nxt = data.select(data.Commercent).where(data.Commercent.id == self._submit_counter).order_by(
                    data.Commercent.id).limit(1)
                row = data.session.execute(nxt).fetchall()
                self.set_text(self.tabWidget_2.currentIndex(), row)
        if self.tabWidget_2.currentIndex() == 1:
            acts = data.session.query(
                data.Activite).order_by(data.Activite.id)
            if self._submit_counter <= acts.count():
                nxt = data.select(data.Activite).where(data.Activite.id == self._submit_counter).order_by(
                    data.Activite.id).limit(1)
                row = data.session.execute(nxt).fetchall()
                self.set_text(self.tabWidget_2.currentIndex(), row)

    def return_Tabe(self):
        self._submit_counter -= 1
        if self.tabWidget_2.currentIndex() == 0:
            acts = data.session.query(
                data.Commercent).order_by(data.Commercent.id)
            if self._submit_counter <= acts.count():
                nxt = data.select(data.Commercent).where(data.Commercent.id == self._submit_counter).order_by(
                    data.Commercent.id).limit(1)
                row = data.session.execute(nxt).fetchall()
                self.set_text(self.tabWidget_2.currentIndex(), row)
        if self.tabWidget_2.currentIndex() == 1:
            acts = data.session.query(
                data.Activite).order_by(data.Activite.id)
            if self._submit_counter <= acts.count():
                nxt = data.select(data.Activite).where(data.Activite.id == self._submit_counter).order_by(
                    data.Activite.id).limit(1)
                row = data.session.execute(nxt).fetchall()
                self.set_text(self.tabWidget_2.currentIndex(), row)

    def _save(self):
        if self.message('هل تريد حفظ في قاعدة البيانات ') == QMessageBox.Yes:
            data.session.commit()
            self.loaddata_com()
            

    def print_data(self):

        pass

    def open_pdf(self):

        path = 'sample01.pdf'
        subprocess.Popen([path], shell=True)

    def closeEvent(self, event):
        if self.message('هل تريد المغادرة فعلا?') == QMessageBox.Yes:
            event.accept()
            data.session.close()
        else:
            event.ignore()

        # SPLASH SCREEN


class SplashScreen(QMainWindow):
    def __init__(self):
        QMainWindow.__init__(self)
        self.ui = Ui_SplashScreen()
        self.ui.setupUi(self)

        ## UI ==> INTERFACE CODES
        ########################################################################

        ## REMOVE TITLE BAR
        self.setWindowFlag(QtCore.Qt.FramelessWindowHint)
        self.setAttribute(QtCore.Qt.WA_TranslucentBackground)

        ## DROP SHADOW EFFECT
        self.shadow = QGraphicsDropShadowEffect(self)
        self.shadow.setBlurRadius(20)
        self.shadow.setXOffset(0)
        self.shadow.setYOffset(0)
        self.shadow.setColor(QColor(0, 0, 0, 60))
        self.ui.dropShadowFrame.setGraphicsEffect(self.shadow)

        ## QTIMER ==> START
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.progress)
        # TIMER IN MILLISECONDS
        self.timer.start(35)

        # CHANGE DESCRIPTION

        # Initial Text
        self.ui.label_description.setText("<strong>  مرحبا بكم في التطبيق </strong> ")

        # Change Texts
        QtCore.QTimer.singleShot(1500,
                                 lambda: self.ui.label_description.setText("<strong> تحميل قاعدة البيانات </strong> "))
        QtCore.QTimer.singleShot(3000,
                                 lambda: self.ui.label_description.setText("<strong>تحميل واجهةالمستخدم</strong> "))

        ## SHOW ==> MAIN WINDOW
        ########################################################################
        self.show()
        ## ==> END ##

    ## ==> APP FUNCTIONS
    ########################################################################
    def progress(self):
        global counter

        # SET VALUE TO PROGRESS BAR
        self.ui.progressBar.setValue(counter)

        # CLOSE SPLASH SCREE AND OPEN APP
        if counter > 100:
            # STOP TIMER
            self.timer.stop()

            # SHOW MAIN WINDOW
            self.main = MainWindow()
            center = QScreen.availableGeometry(QApplication.primaryScreen()).center()
            geo = self.main.frameGeometry()
            geo.moveCenter(center)
            self.main.move(200, 10)
            self.main.show()

            # CLOSE SPLASH SCREEN
            self.close()

        # INCREASE COUNTER
        counter += 1


if __name__ == "__main__":
    app = QApplication([])
    window = SplashScreen()
    sys.exit(app.exec())

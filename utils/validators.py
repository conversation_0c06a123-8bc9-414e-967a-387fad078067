"""
Input validation utilities for SCTX Application
"""
import re
from datetime import date, datetime
from typing import Optional, Union, List, Dict, Any


class ValidationError(Exception):
    """Custom validation error"""
    pass


class InputValidator:
    """Input validation utilities"""
    
    @staticmethod
    def validate_required_field(value: Any, field_name: str) -> str:
        """Validate that a required field is not empty"""
        if not value or (isinstance(value, str) and not value.strip()):
            raise ValidationError(f"{field_name} is required and cannot be empty")
        return str(value).strip()
    
    @staticmethod
    def validate_string_length(value: str, field_name: str, min_length: int = 1, max_length: int = 255) -> str:
        """Validate string length"""
        if len(value) < min_length:
            raise ValidationError(f"{field_name} must be at least {min_length} characters long")
        if len(value) > max_length:
            raise ValidationError(f"{field_name} must be no more than {max_length} characters long")
        return value
    
    @staticmethod
    def validate_registration_number(reg_num: str) -> str:
        """Validate registration number format"""
        reg_num = InputValidator.validate_required_field(reg_num, "Registration number")
        reg_num = InputValidator.validate_string_length(reg_num, "Registration number", 1, 50)
        
        # Basic format validation (adjust pattern as needed)
        if not re.match(r'^[A-Za-z0-9\-/]+$', reg_num):
            raise ValidationError("Registration number contains invalid characters")
        
        return reg_num
    
    @staticmethod
    def validate_name(name: str, field_name: str) -> str:
        """Validate person/entity name"""
        name = InputValidator.validate_required_field(name, field_name)
        name = InputValidator.validate_string_length(name, field_name, 2, 50)
        
        # Allow letters, spaces, hyphens, and apostrophes
        if not re.match(r'^[A-Za-z\u0600-\u06FF\s\-\'\.]+$', name):
            raise ValidationError(f"{field_name} contains invalid characters")
        
        return name
    
    @staticmethod
    def validate_date(date_value: Union[str, date, None], field_name: str, required: bool = False) -> Optional[date]:
        """Validate date input"""
        if date_value is None:
            if required:
                raise ValidationError(f"{field_name} is required")
            return None
        
        if isinstance(date_value, date):
            return date_value
        
        if isinstance(date_value, str):
            if not date_value.strip():
                if required:
                    raise ValidationError(f"{field_name} is required")
                return None
            
            try:
                # Try to parse the date string
                parsed_date = datetime.strptime(date_value.strip(), "%Y-%m-%d").date()
                
                # Validate date range (reasonable business dates)
                if parsed_date.year < 1900 or parsed_date.year > 2100:
                    raise ValidationError(f"{field_name} must be between 1900 and 2100")
                
                return parsed_date
            except ValueError:
                raise ValidationError(f"{field_name} must be in YYYY-MM-DD format")
        
        raise ValidationError(f"{field_name} has invalid format")
    
    @staticmethod
    def validate_address(address: Optional[str], field_name: str) -> Optional[str]:
        """Validate address field"""
        if not address or not address.strip():
            return None
        
        address = address.strip()
        InputValidator.validate_string_length(address, field_name, 1, 100)
        
        return address
    
    @staticmethod
    def validate_activity_code(code: str) -> str:
        """Validate activity code"""
        code = InputValidator.validate_required_field(code, "Activity code")
        code = InputValidator.validate_string_length(code, "Activity code", 1, 20)
        
        # Allow alphanumeric characters, hyphens, and dots
        if not re.match(r'^[A-Za-z0-9\-\.]+$', code):
            raise ValidationError("Activity code contains invalid characters")
        
        return code
    
    @staticmethod
    def validate_pv_number(pv_number: Union[str, int]) -> int:
        """Validate PV number"""
        if isinstance(pv_number, str):
            pv_number = InputValidator.validate_required_field(pv_number, "PV number")
            try:
                pv_number = int(pv_number)
            except ValueError:
                raise ValidationError("PV number must be a valid integer")
        
        if not isinstance(pv_number, int) or pv_number <= 0:
            raise ValidationError("PV number must be a positive integer")
        
        return pv_number
    
    @staticmethod
    def validate_amount(amount: Union[str, float, int]) -> float:
        """Validate monetary amount"""
        if isinstance(amount, str):
            amount = InputValidator.validate_required_field(amount, "Amount")
            try:
                amount = float(amount)
            except ValueError:
                raise ValidationError("Amount must be a valid number")
        
        if not isinstance(amount, (int, float)) or amount < 0:
            raise ValidationError("Amount must be a non-negative number")
        
        # Round to 2 decimal places
        return round(float(amount), 2)
    
    @staticmethod
    def validate_year(year: Union[str, int]) -> int:
        """Validate year"""
        if isinstance(year, str):
            year = InputValidator.validate_required_field(year, "Year")
            try:
                year = int(year)
            except ValueError:
                raise ValidationError("Year must be a valid integer")
        
        current_year = datetime.now().year
        if not isinstance(year, int) or year < 2000 or year > current_year + 10:
            raise ValidationError(f"Year must be between 2000 and {current_year + 10}")
        
        return year
    
    @staticmethod
    def validate_commercent_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate complete commercent data"""
        validated_data = {}
        
        # Required fields
        validated_data['Num_reg'] = InputValidator.validate_registration_number(data.get('Num_reg', ''))
        validated_data['Nom_com'] = InputValidator.validate_name(data.get('Nom_com', ''), "Last name")
        validated_data['Prenom_com'] = InputValidator.validate_name(data.get('Prenom_com', ''), "First name")
        
        # Optional fields
        validated_data['Date_nai'] = InputValidator.validate_date(data.get('Date_nai'), "Birth date")
        validated_data['Lieu_nai'] = InputValidator.validate_address(data.get('Lieu_nai'), "Birth place")
        validated_data['Adresse_dom'] = InputValidator.validate_address(data.get('Adresse_dom'), "Home address")
        validated_data['Adresse_local'] = InputValidator.validate_address(data.get('Adresse_local'), "Business address")
        
        # Parent names (optional)
        if data.get('Prenom_per'):
            validated_data['Prenom_per'] = InputValidator.validate_name(data['Prenom_per'], "Father's first name")
        if data.get('Nom_mer'):
            validated_data['Nom_mer'] = InputValidator.validate_name(data['Nom_mer'], "Mother's last name")
        if data.get('Prenom_mer'):
            validated_data['Prenom_mer'] = InputValidator.validate_name(data['Prenom_mer'], "Mother's first name")
        
        # Registration date and status
        validated_data['Date_reg'] = InputValidator.validate_date(data.get('Date_reg'), "Registration date")
        if data.get('Etat'):
            validated_data['Etat'] = InputValidator.validate_string_length(data['Etat'], "Status", 1, 20)
        
        return validated_data
    
    @staticmethod
    def validate_pv_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate complete PV data"""
        validated_data = {}
        
        # Required fields
        validated_data['nume_pv'] = InputValidator.validate_pv_number(data.get('nume_pv', 0))
        validated_data['infraction'] = InputValidator.validate_required_field(data.get('infraction', ''), "Infraction")
        
        # Optional date fields
        validated_data['date_pv'] = InputValidator.validate_date(data.get('date_pv'), "PV date")
        validated_data['date_constatation'] = InputValidator.validate_date(data.get('date_constatation'), "Observation date")
        validated_data['date_envoi_jus'] = InputValidator.validate_date(data.get('date_envoi_jus'), "Justice sending date")
        validated_data['date_audience'] = InputValidator.validate_date(data.get('date_audience'), "Hearing date")
        
        # Year
        if data.get('annee'):
            validated_data['annee'] = InputValidator.validate_year(data['annee'])
        
        return validated_data
    
    @staticmethod
    def validate_avertissement_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate complete avertissement data"""
        validated_data = {}
        
        # Required fields
        validated_data['nume_recu'] = InputValidator.validate_pv_number(data.get('nume_recu', 0))
        validated_data['montant'] = InputValidator.validate_amount(data.get('montant', 0))
        
        # Optional date fields
        validated_data['date_avert'] = InputValidator.validate_date(data.get('date_avert'), "Warning date")
        validated_data['date_recu'] = InputValidator.validate_date(data.get('date_recu'), "Receipt date")
        
        return validated_data

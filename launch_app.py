#!/usr/bin/env python3
"""
Simple launcher for the SCTX application
This script handles the application startup with proper error handling
"""
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import sqlalchemy
    except ImportError:
        missing_deps.append("sqlalchemy")
    
    try:
        from PySide6.QtWidgets import QApplication
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        from dateutil import parser
    except ImportError:
        missing_deps.append("python-dateutil")
    
    return missing_deps

def install_dependencies(missing_deps):
    """Install missing dependencies"""
    print("Installing missing dependencies...")
    import subprocess
    
    for dep in missing_deps:
        try:
            print(f"Installing {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
            print(f"✓ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {dep}: {e}")
            return False
    
    return True

def run_architecture_test():
    """Run the architecture test to verify everything works"""
    print("Running architecture validation...")
    try:
        import test_new_architecture
        result = test_new_architecture.main()
        return result == 0
    except Exception as e:
        print(f"Architecture test failed: {e}")
        return False

def launch_gui_app():
    """Launch the GUI application"""
    print("Launching SCTX Management System...")
    try:
        # Import and run the main application
        import main
        main.main()
    except ImportError as e:
        print(f"Failed to import main application: {e}")
        print("This might be due to missing UI files from the original application.")
        print("You can still use the improved architecture with the demo script:")
        print("python run_improved_app.py")
        return False
    except Exception as e:
        print(f"Failed to launch application: {e}")
        import traceback
        traceback.print_exc()
        return False

def launch_demo():
    """Launch the demo application"""
    print("Launching architecture demo...")
    try:
        import run_improved_app
        run_improved_app.main()
    except Exception as e:
        print(f"Failed to launch demo: {e}")
        return False

def main():
    """Main launcher function"""
    print("🚀 SCTX Management System v2.0 Launcher")
    print("=" * 50)
    
    # Check dependencies
    missing_deps = check_dependencies()
    
    if missing_deps:
        print(f"Missing dependencies: {', '.join(missing_deps)}")
        
        install_choice = input("Would you like to install them automatically? (y/n): ").lower().strip()
        if install_choice == 'y':
            if not install_dependencies(missing_deps):
                print("Failed to install dependencies. Please install them manually:")
                print(f"pip install {' '.join(missing_deps)}")
                return 1
        else:
            print("Please install the missing dependencies manually:")
            print(f"pip install {' '.join(missing_deps)}")
            return 1
    
    # Run architecture test
    print("\n🧪 Testing architecture...")
    if not run_architecture_test():
        print("Architecture test failed. Please check the error messages above.")
        return 1
    
    print("\n✅ Architecture test passed!")
    
    # Choose what to launch
    print("\nChoose launch option:")
    print("1. Launch full GUI application (requires original UI files)")
    print("2. Launch architecture demo (works without UI files)")
    print("3. Exit")
    
    while True:
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            if launch_gui_app():
                return 0
            else:
                print("\nFalling back to demo mode...")
                return launch_demo()
        elif choice == "2":
            launch_demo()
            return 0
        elif choice == "3":
            print("👋 Goodbye!")
            return 0
        else:
            print("❌ Invalid choice. Please enter 1, 2, or 3.")

if __name__ == "__main__":
    sys.exit(main())

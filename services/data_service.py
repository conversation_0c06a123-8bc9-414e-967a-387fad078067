"""
Data Service Layer for SCTX Application
Provides high-level data operations and business logic
"""
import logging
from typing import List, Optional, Dict, Any, Type
from datetime import date
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, or_, func

from database.database_manager import DatabaseManager
from models.models import Commercent, Activite, Activite_Tab, Infraction, Pv, Avertisment

logger = logging.getLogger(__name__)


class DataService:
    """Service class for data operations"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db_manager = db_manager
    
    # Commercent operations
    def get_all_commercents(self, limit: Optional[int] = None, offset: int = 0) -> List[Commercent]:
        """Get all commercial entities with optional pagination"""
        try:
            with self.db_manager.get_session() as session:
                query = session.query(Commercent).order_by(Commercent.id)
                if limit:
                    query = query.limit(limit).offset(offset)
                return query.all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get commercents: {e}")
            return []
    
    def get_commercent_by_id(self, commercent_id: int) -> Optional[Commercent]:
        """Get a commercial entity by ID"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Commercent).filter(Commercent.id == commercent_id).first()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get commercent by ID {commercent_id}: {e}")
            return None
    
    def search_commercents(self, search_term: str, search_field: str = "Nom_com") -> List[Commercent]:
        """Search commercial entities by field"""
        try:
            with self.db_manager.get_session() as session:
                field = getattr(Commercent, search_field, Commercent.Nom_com)
                return session.query(Commercent).filter(
                    field.ilike(f'%{search_term}%')
                ).order_by(Commercent.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to search commercents: {e}")
            return []
    
    def create_commercent(self, data: Dict[str, Any]) -> Optional[Commercent]:
        """Create a new commercial entity"""
        try:
            with self.db_manager.get_session() as session:
                commercent = Commercent(**data)
                session.add(commercent)
                session.flush()  # Get the ID
                session.refresh(commercent)
                return commercent
        except SQLAlchemyError as e:
            logger.error(f"Failed to create commercent: {e}")
            return None
    
    def update_commercent(self, commercent_id: int, data: Dict[str, Any]) -> bool:
        """Update a commercial entity"""
        try:
            with self.db_manager.get_session() as session:
                commercent = session.query(Commercent).filter(Commercent.id == commercent_id).first()
                if commercent:
                    for key, value in data.items():
                        if hasattr(commercent, key):
                            setattr(commercent, key, value)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to update commercent {commercent_id}: {e}")
            return False
    
    def delete_commercent(self, commercent_id: int) -> bool:
        """Delete a commercial entity"""
        try:
            with self.db_manager.get_session() as session:
                commercent = session.query(Commercent).filter(Commercent.id == commercent_id).first()
                if commercent:
                    session.delete(commercent)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to delete commercent {commercent_id}: {e}")
            return False
    
    # Activity operations
    def get_activities_by_commercent(self, commercent_id: int) -> List[Activite]:
        """Get all activities for a commercial entity"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Activite).filter(
                    Activite.parent_id == commercent_id
                ).order_by(Activite.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get activities for commercent {commercent_id}: {e}")
            return []
    
    def get_all_activity_templates(self) -> List[Activite_Tab]:
        """Get all activity templates"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Activite_Tab).order_by(Activite_Tab.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get activity templates: {e}")
            return []
    
    def create_activity(self, code: str, name: str, commercent_id: int) -> Optional[Activite]:
        """Create a new activity"""
        try:
            with self.db_manager.get_session() as session:
                activity = Activite(Code_act=code, Nom_act=name, parent_id=commercent_id)
                session.add(activity)
                session.flush()
                session.refresh(activity)
                return activity
        except SQLAlchemyError as e:
            logger.error(f"Failed to create activity: {e}")
            return None
    
    def create_activity_template(self, name: str) -> Optional[Activite_Tab]:
        """Create a new activity template"""
        try:
            with self.db_manager.get_session() as session:
                template = Activite_Tab(Nom_act=name)
                session.add(template)
                session.flush()
                session.refresh(template)
                return template
        except SQLAlchemyError as e:
            logger.error(f"Failed to create activity template: {e}")
            return None

    def update_activity(self, activity_id: int, data: Dict[str, Any]) -> bool:
        """Update an activity"""
        try:
            with self.db_manager.get_session() as session:
                activity = session.query(Activite).filter(Activite.id == activity_id).first()
                if activity:
                    for key, value in data.items():
                        if hasattr(activity, key):
                            setattr(activity, key, value)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to update activity {activity_id}: {e}")
            return False

    def delete_activity(self, activity_id: int) -> bool:
        """Delete an activity"""
        try:
            with self.db_manager.get_session() as session:
                activity = session.query(Activite).filter(Activite.id == activity_id).first()
                if activity:
                    session.delete(activity)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to delete activity {activity_id}: {e}")
            return False
    
    # Infraction operations
    def get_all_infractions(self) -> List[Infraction]:
        """Get all infractions"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Infraction).order_by(Infraction.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get infractions: {e}")
            return []
    
    def search_infractions(self, search_term: str) -> List[Infraction]:
        """Search infractions by name"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Infraction).filter(
                    Infraction.Nom_inf.ilike(f'%{search_term}%')
                ).order_by(Infraction.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to search infractions: {e}")
            return []
    
    def create_infraction(self, name: str) -> Optional[Infraction]:
        """Create a new infraction"""
        try:
            with self.db_manager.get_session() as session:
                infraction = Infraction(Nom_inf=name)
                session.add(infraction)
                session.flush()
                session.refresh(infraction)
                return infraction
        except SQLAlchemyError as e:
            logger.error(f"Failed to create infraction: {e}")
            return None

    def update_infraction(self, infraction_id: int, name: str) -> bool:
        """Update an infraction"""
        try:
            with self.db_manager.get_session() as session:
                infraction = session.query(Infraction).filter(Infraction.id == infraction_id).first()
                if infraction:
                    infraction.Nom_inf = name
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to update infraction {infraction_id}: {e}")
            return False

    def delete_infraction(self, infraction_id: int) -> bool:
        """Delete an infraction"""
        try:
            with self.db_manager.get_session() as session:
                infraction = session.query(Infraction).filter(Infraction.id == infraction_id).first()
                if infraction:
                    session.delete(infraction)
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to delete infraction {infraction_id}: {e}")
            return False
    
    # PV operations
    def get_pvs_by_commercent(self, commercent_id: int) -> List[Pv]:
        """Get all PVs for a commercial entity"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Pv).filter(
                    Pv.parent_id == commercent_id
                ).order_by(Pv.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get PVs for commercent {commercent_id}: {e}")
            return []
    
    def create_pv(self, data: Dict[str, Any], commercent_id: int) -> Optional[Pv]:
        """Create a new PV"""
        try:
            with self.db_manager.get_session() as session:
                pv = Pv(**data, parent_id=commercent_id)
                session.add(pv)
                session.flush()
                session.refresh(pv)
                return pv
        except SQLAlchemyError as e:
            logger.error(f"Failed to create PV: {e}")
            return None
    
    # Avertissement operations
    def get_avertissements_by_pv(self, pv_id: int) -> List[Avertisment]:
        """Get all warnings for a PV"""
        try:
            with self.db_manager.get_session() as session:
                return session.query(Avertisment).filter(
                    Avertisment.parent_id == pv_id
                ).order_by(Avertisment.id).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get avertissements for PV {pv_id}: {e}")
            return []
    
    def create_avertissement(self, data: Dict[str, Any], pv_id: int) -> Optional[Avertisment]:
        """Create a new warning"""
        try:
            with self.db_manager.get_session() as session:
                avertissement = Avertisment(**data, parent_id=pv_id)
                session.add(avertissement)
                session.flush()
                session.refresh(avertissement)
                return avertissement
        except SQLAlchemyError as e:
            logger.error(f"Failed to create avertissement: {e}")
            return None
    
    # Statistics and reporting
    def get_statistics(self) -> Dict[str, int]:
        """Get application statistics"""
        try:
            with self.db_manager.get_session() as session:
                stats = {
                    'total_commercents': session.query(func.count(Commercent.id)).scalar(),
                    'total_activities': session.query(func.count(Activite.id)).scalar(),
                    'total_infractions': session.query(func.count(Infraction.id)).scalar(),
                    'total_pvs': session.query(func.count(Pv.id)).scalar(),
                    'total_avertissements': session.query(func.count(Avertisment.id)).scalar(),
                }
                return stats
        except SQLAlchemyError as e:
            logger.error(f"Failed to get statistics: {e}")
            return {}
    
    def commit_changes(self) -> bool:
        """Commit all pending changes"""
        try:
            with self.db_manager.get_session() as session:
                # Session auto-commits in context manager
                return True
        except SQLAlchemyError as e:
            logger.error(f"Failed to commit changes: {e}")
            return False

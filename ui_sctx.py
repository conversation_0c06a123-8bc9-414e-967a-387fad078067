# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'sctxtBAWrA.ui'
##
## Created by: Qt User Interface Compiler version 6.4.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QAction, QBrush, QColor, QConicalGradient,
    QCursor, QFont, QFontDatabase, QGradient,
    QIcon, QImage, QKeySequence, QLinearGradient,
    QPainter, QPalette, QPixmap, QRadialGradient,
    QTransform)
from PySide6.QtWidgets import (QAbstractItemView, QAbstractSpinBox, QApplication, QComboBox,
    QDateEdit, QFrame, QGridLayout, QHeaderView,
    QLCDNumber, QLabel, QLineEdit, QMainWindow,
    QMenu, QMenuBar, QPushButton, QSizePolicy,
    QSpacerItem, QTabWidget, QTableWidget, QTableWidgetItem,
    QToolBar, QVBoxLayout, QWidget)
import images_rc

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.setEnabled(True)
        MainWindow.resize(978, 621)
        sizePolicy = QSizePolicy(QSizePolicy.Fixed, QSizePolicy.Fixed)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(MainWindow.sizePolicy().hasHeightForWidth())
        MainWindow.setSizePolicy(sizePolicy)
        MainWindow.setMinimumSize(QSize(0, 0))
        MainWindow.setSizeIncrement(QSize(0, 0))
        MainWindow.setBaseSize(QSize(0, 0))
        font = QFont()
        font.setFamilies([u"Tahoma"])
        font.setPointSize(9)
        MainWindow.setFont(font)
        icon = QIcon()
        icon.addFile(u":/image/img/siege.jpg", QSize(), QIcon.Normal, QIcon.Off)
        MainWindow.setWindowIcon(icon)
        MainWindow.setAutoFillBackground(True)
        MainWindow.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        MainWindow.setTabShape(QTabWidget.Rounded)
        MainWindow.setDockOptions(QMainWindow.AllowTabbedDocks|QMainWindow.AnimatedDocks)
        MainWindow.setUnifiedTitleAndToolBarOnMac(False)
        self.actioncommercent = QAction(MainWindow)
        self.actioncommercent.setObjectName(u"actioncommercent")
        icon1 = QIcon()
        icon1.addFile(u":/image/img/icons8_administrator_male.ico", QSize(), QIcon.Normal, QIcon.Off)
        self.actioncommercent.setIcon(icon1)
        font1 = QFont()
        font1.setPointSize(10)
        self.actioncommercent.setFont(font1)
        self.actionactevite = QAction(MainWindow)
        self.actionactevite.setObjectName(u"actionactevite")
        icon2 = QIcon()
        icon2.addFile(u":/image/img/fast_cart_50px.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionactevite.setIcon(icon2)
        self.actionactevite.setFont(font1)
        self.actioninfraction = QAction(MainWindow)
        self.actioninfraction.setObjectName(u"actioninfraction")
        icon3 = QIcon()
        icon3.addFile(u":/image/img/icons8_briefcase_32.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actioninfraction.setIcon(icon3)
        self.actioninfraction.setFont(font1)
        self.actionAjouter = QAction(MainWindow)
        self.actionAjouter.setObjectName(u"actionAjouter")
        icon4 = QIcon()
        icon4.addFile(u":/image/img/trombone.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionAjouter.setIcon(icon4)
        self.actionModifier = QAction(MainWindow)
        self.actionModifier.setObjectName(u"actionModifier")
        icon5 = QIcon()
        icon5.addFile(u":/image/img/liste-de-controle.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionModifier.setIcon(icon5)
        self.actionSuprimer = QAction(MainWindow)
        self.actionSuprimer.setObjectName(u"actionSuprimer")
        icon6 = QIcon()
        icon6.addFile(u":/image/img/ciseaux.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionSuprimer.setIcon(icon6)
        self.actionImprimer = QAction(MainWindow)
        self.actionImprimer.setObjectName(u"actionImprimer")
        icon7 = QIcon()
        icon7.addFile(u":/image/img/dechiqueteuse.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionImprimer.setIcon(icon7)
        self.actionClose = QAction(MainWindow)
        self.actionClose.setObjectName(u"actionClose")
        icon8 = QIcon()
        icon8.addFile(u":/image/img/power-button.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionClose.setIcon(icon8)
        self.actionSave = QAction(MainWindow)
        self.actionSave.setObjectName(u"actionSave")
        icon9 = QIcon()
        icon9.addFile(u":/image/img/binder_64px.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionSave.setIcon(icon9)
        self.actionNouveau = QAction(MainWindow)
        self.actionNouveau.setObjectName(u"actionNouveau")
        icon10 = QIcon()
        icon10.addFile(u":/image/img/plus.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionNouveau.setIcon(icon10)
        self.actionHide = QAction(MainWindow)
        self.actionHide.setObjectName(u"actionHide")
        icon11 = QIcon()
        icon11.addFile(u":/image/img/double_left_64px.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionHide.setIcon(icon11)
        self.actionShow = QAction(MainWindow)
        self.actionShow.setObjectName(u"actionShow")
        icon12 = QIcon()
        icon12.addFile(u":/image/img/double_right_64px.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionShow.setIcon(icon12)
        self.actiontable_Activite = QAction(MainWindow)
        self.actiontable_Activite.setObjectName(u"actiontable_Activite")
        icon13 = QIcon()
        icon13.addFile(u":/image/img/house.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actiontable_Activite.setIcon(icon13)
        self.actiontable_Activite.setFont(font1)
        self.actionNext = QAction(MainWindow)
        self.actionNext.setObjectName(u"actionNext")
        icon14 = QIcon()
        icon14.addFile(u":/image/img/icons8_double_right_16.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionNext.setIcon(icon14)
        self.actionReturn = QAction(MainWindow)
        self.actionReturn.setObjectName(u"actionReturn")
        icon15 = QIcon()
        icon15.addFile(u":/image/img/icons8-u-turn-to-left-48.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionReturn.setIcon(icon15)
        self.actiontable_Infraction = QAction(MainWindow)
        self.actiontable_Infraction.setObjectName(u"actiontable_Infraction")
        self.actiontable_Infraction.setIcon(icon5)
        self.actiontable_Infraction.setFont(font1)
        self.actionPv = QAction(MainWindow)
        self.actionPv.setObjectName(u"actionPv")
        icon16 = QIcon()
        icon16.addFile(u":/image/img/icons8_briefcase_16.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionPv.setIcon(icon16)
        self.actionPv.setFont(font1)
        self.actionAfaire = QAction(MainWindow)
        self.actionAfaire.setObjectName(u"actionAfaire")
        icon17 = QIcon()
        icon17.addFile(u":/image/img/button.png", QSize(), QIcon.Normal, QIcon.Off)
        self.actionAfaire.setIcon(icon17)
        self.actionAfaire.setFont(font1)
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.gridLayout_4 = QGridLayout(self.centralwidget)
        self.gridLayout_4.setObjectName(u"gridLayout_4")
        self.tableWidget_com = QTableWidget(self.centralwidget)
        if (self.tableWidget_com.columnCount() < 13):
            self.tableWidget_com.setColumnCount(13)
        __qtablewidgetitem = QTableWidgetItem()
        __qtablewidgetitem.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(0, __qtablewidgetitem)
        __qtablewidgetitem1 = QTableWidgetItem()
        __qtablewidgetitem1.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        __qtablewidgetitem2 = QTableWidgetItem()
        __qtablewidgetitem2.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(2, __qtablewidgetitem2)
        __qtablewidgetitem3 = QTableWidgetItem()
        __qtablewidgetitem3.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(3, __qtablewidgetitem3)
        __qtablewidgetitem4 = QTableWidgetItem()
        __qtablewidgetitem4.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(4, __qtablewidgetitem4)
        __qtablewidgetitem5 = QTableWidgetItem()
        __qtablewidgetitem5.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(5, __qtablewidgetitem5)
        __qtablewidgetitem6 = QTableWidgetItem()
        __qtablewidgetitem6.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(6, __qtablewidgetitem6)
        __qtablewidgetitem7 = QTableWidgetItem()
        __qtablewidgetitem7.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(7, __qtablewidgetitem7)
        __qtablewidgetitem8 = QTableWidgetItem()
        __qtablewidgetitem8.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(8, __qtablewidgetitem8)
        __qtablewidgetitem9 = QTableWidgetItem()
        __qtablewidgetitem9.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(9, __qtablewidgetitem9)
        __qtablewidgetitem10 = QTableWidgetItem()
        __qtablewidgetitem10.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(10, __qtablewidgetitem10)
        __qtablewidgetitem11 = QTableWidgetItem()
        __qtablewidgetitem11.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(11, __qtablewidgetitem11)
        __qtablewidgetitem12 = QTableWidgetItem()
        __qtablewidgetitem12.setFont(font1);
        self.tableWidget_com.setHorizontalHeaderItem(12, __qtablewidgetitem12)
        self.tableWidget_com.setObjectName(u"tableWidget_com")
        self.tableWidget_com.setEnabled(True)
        self.tableWidget_com.setLayoutDirection(Qt.RightToLeft)
        self.tableWidget_com.setStyleSheet(u"background-color: qconicalgradient(cx:0, cy:0, angle:135, stop:0 rgba(255, 255, 0, 69), stop:0.375 rgba(255, 255, 0, 69), stop:0.423533 rgba(251, 255, 0, 145), stop:0.45 rgba(247, 255, 0, 208), stop:0.477581 rgba(255, 244, 71, 130), stop:0.518717 rgba(255, 218, 71, 130), stop:0.55 rgba(255, 255, 0, 255), stop:0.57754 rgba(255, 203, 0, 130), stop:0.625 rgba(255, 255, 0, 69), stop:1 rgba(255, 255, 0, 69));")
        self.tableWidget_com.setSelectionBehavior(QAbstractItemView.SelectRows)

        self.gridLayout_4.addWidget(self.tableWidget_com, 2, 1, 1, 1)

        self.tabWidget_2 = QTabWidget(self.centralwidget)
        self.tabWidget_2.setObjectName(u"tabWidget_2")
        self.tabWidget_2.setStyleSheet(u"background-color: qlineargradient(spread:reflect, x1:0.629, y1:0.392045, x2:0, y2:1, stop:0.696682 rgba(44, 64, 174, 255), stop:0.981043 rgba(255, 255, 255, 255));")
        self.tabWidget_2.setTabPosition(QTabWidget.West)
        self.ajou_com = QWidget()
        self.ajou_com.setObjectName(u"ajou_com")
        self.gridLayout_3 = QGridLayout(self.ajou_com)
        self.gridLayout_3.setObjectName(u"gridLayout_3")
        self.frame_2 = QFrame(self.ajou_com)
        self.frame_2.setObjectName(u"frame_2")
        self.frame_2.setFrameShape(QFrame.StyledPanel)
        self.frame_2.setFrameShadow(QFrame.Raised)
        self.verticalLayout_3 = QVBoxLayout(self.frame_2)
        self.verticalLayout_3.setObjectName(u"verticalLayout_3")
        self.verticalLayout_5 = QVBoxLayout()
        self.verticalLayout_5.setSpacing(13)
        self.verticalLayout_5.setObjectName(u"verticalLayout_5")
        self.reg_com = QLineEdit(self.frame_2)
        self.reg_com.setObjectName(u"reg_com")
        self.reg_com.setMaximumSize(QSize(16777215, 38))
        self.reg_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.reg_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.reg_com)

        self.nom_com = QLineEdit(self.frame_2)
        self.nom_com.setObjectName(u"nom_com")
        self.nom_com.setMaximumSize(QSize(16777215, 38))
        self.nom_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.nom_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.nom_com)

        self.prenom_com = QLineEdit(self.frame_2)
        self.prenom_com.setObjectName(u"prenom_com")
        self.prenom_com.setMaximumSize(QSize(16777215, 38))
        self.prenom_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.prenom_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.prenom_com)

        self.label = QLabel(self.frame_2)
        self.label.setObjectName(u"label")
        self.label.setMaximumSize(QSize(16777215, 38))
        self.label.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.verticalLayout_5.addWidget(self.label)

        self.date_nai_com = QDateEdit(self.frame_2)
        self.date_nai_com.setObjectName(u"date_nai_com")
        self.date_nai_com.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_nai_com.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.date_nai_com.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.date_nai_com.setCalendarPopup(True)

        self.verticalLayout_5.addWidget(self.date_nai_com)

        self.lieu_nai_com = QLineEdit(self.frame_2)
        self.lieu_nai_com.setObjectName(u"lieu_nai_com")
        self.lieu_nai_com.setMaximumSize(QSize(16777215, 38))
        self.lieu_nai_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.lieu_nai_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.lieu_nai_com)

        self.prenomPer_com = QLineEdit(self.frame_2)
        self.prenomPer_com.setObjectName(u"prenomPer_com")
        self.prenomPer_com.setMaximumSize(QSize(16777215, 38))
        self.prenomPer_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.prenomPer_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.prenomPer_com)

        self.nomMe_com = QLineEdit(self.frame_2)
        self.nomMe_com.setObjectName(u"nomMe_com")
        self.nomMe_com.setMaximumSize(QSize(16777215, 38))
        self.nomMe_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.nomMe_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.nomMe_com)

        self.prenomMer_com = QLineEdit(self.frame_2)
        self.prenomMer_com.setObjectName(u"prenomMer_com")
        self.prenomMer_com.setMaximumSize(QSize(16777215, 38))
        self.prenomMer_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.prenomMer_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.prenomMer_com)

        self.Adresse_com = QLineEdit(self.frame_2)
        self.Adresse_com.setObjectName(u"Adresse_com")
        self.Adresse_com.setMaximumSize(QSize(16777215, 38))
        self.Adresse_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.Adresse_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.Adresse_com)

        self.adresseLoc_com = QLineEdit(self.frame_2)
        self.adresseLoc_com.setObjectName(u"adresseLoc_com")
        self.adresseLoc_com.setMaximumSize(QSize(16777215, 38))
        self.adresseLoc_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.adresseLoc_com.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.adresseLoc_com)

        self.label_4 = QLabel(self.frame_2)
        self.label_4.setObjectName(u"label_4")
        self.label_4.setMaximumSize(QSize(381, 30))
        self.label_4.setLayoutDirection(Qt.LeftToRight)
        self.label_4.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")
        self.label_4.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.verticalLayout_5.addWidget(self.label_4)

        self.dateReg_com = QDateEdit(self.frame_2)
        self.dateReg_com.setObjectName(u"dateReg_com")
        font2 = QFont()
        font2.setPointSize(9)
        self.dateReg_com.setFont(font2)
        self.dateReg_com.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.dateReg_com.setCorrectionMode(QAbstractSpinBox.CorrectToPreviousValue)
        self.dateReg_com.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.dateReg_com.setCalendarPopup(True)

        self.verticalLayout_5.addWidget(self.dateReg_com)

        self.setuation_com = QComboBox(self.frame_2)
        self.setuation_com.addItem("")
        self.setuation_com.addItem("")
        self.setuation_com.setObjectName(u"setuation_com")
        self.setuation_com.setStyleSheet(u"font: 700 10pt \"Segoe UI\";")

        self.verticalLayout_5.addWidget(self.setuation_com)


        self.verticalLayout_3.addLayout(self.verticalLayout_5)


        self.gridLayout_3.addWidget(self.frame_2, 0, 0, 1, 1)

        self.tabWidget_2.addTab(self.ajou_com, "")
        self.ajou_act = QWidget()
        self.ajou_act.setObjectName(u"ajou_act")
        self.gridLayout_2 = QGridLayout(self.ajou_act)
        self.gridLayout_2.setObjectName(u"gridLayout_2")
        self.frame = QFrame(self.ajou_act)
        self.frame.setObjectName(u"frame")
        self.frame.setFrameShape(QFrame.HLine)
        self.frame.setFrameShadow(QFrame.Raised)
        self.gridLayout = QGridLayout(self.frame)
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setHorizontalSpacing(6)
        self.gridLayout.setContentsMargins(2, 2, 2, 2)
        self.code_actevite = QLineEdit(self.frame)
        self.code_actevite.setObjectName(u"code_actevite")
        self.code_actevite.setMaximumSize(QSize(16777215, 31))
        self.code_actevite.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);\n"
"")
        self.code_actevite.setAlignment(Qt.AlignLeading|Qt.AlignLeft|Qt.AlignVCenter)

        self.gridLayout.addWidget(self.code_actevite, 2, 0, 1, 1)

        self.comboBox_act = QComboBox(self.frame)
        self.comboBox_act.setObjectName(u"comboBox_act")
        self.comboBox_act.setLayoutDirection(Qt.RightToLeft)
        self.comboBox_act.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);")

        self.gridLayout.addWidget(self.comboBox_act, 1, 0, 1, 1)

        self.Button_actuelise = QPushButton(self.frame)
        self.Button_actuelise.setObjectName(u"Button_actuelise")
        self.Button_actuelise.setStyleSheet(u"QPushButton{\n"
"            border-style:outset;\n"
"            border-with : 2px;\n"
"            border-radius:10px;\n"
"            border-color: beige;\n"
"            font:bold 14px;\n"
"            min-with : 10 em;\n"
"            padding : 6px;\n"
"            transition: 0.5s;\n"
"            }\n"
"            QPushButton::hover{\n"
"            background-color:#1aff6e;\n"
"            letter-spacing: 1px;\n"
"            }")
        self.Button_actuelise.setIcon(icon15)

        self.gridLayout.addWidget(self.Button_actuelise, 0, 0, 1, 1)


        self.gridLayout_2.addWidget(self.frame, 0, 1, 1, 1)

        self.tabWidget_2.addTab(self.ajou_act, "")
        self.tab = QWidget()
        self.tab.setObjectName(u"tab")
        self.gridLayout_7 = QGridLayout(self.tab)
        self.gridLayout_7.setObjectName(u"gridLayout_7")
        self.label_8 = QLabel(self.tab)
        self.label_8.setObjectName(u"label_8")
        self.label_8.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_7.addWidget(self.label_8, 4, 0, 1, 2)

        self.infraction_pv = QComboBox(self.tab)
        self.infraction_pv.setObjectName(u"infraction_pv")
        font3 = QFont()
        font3.setFamilies([u"Segoe UI"])
        font3.setPointSize(12)
        font3.setBold(True)
        font3.setItalic(False)
        self.infraction_pv.setFont(font3)
        self.infraction_pv.setLayoutDirection(Qt.RightToLeft)
        self.infraction_pv.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_7.addWidget(self.infraction_pv, 10, 0, 1, 2)

        self.numero_pv = QLineEdit(self.tab)
        self.numero_pv.setObjectName(u"numero_pv")
        self.numero_pv.setEnabled(True)
        font4 = QFont()
        font4.setPointSize(12)
        font4.setBold(True)
        self.numero_pv.setFont(font4)
        self.numero_pv.setStyleSheet(u"background-color: qlineargradient(spread:reflect, x1:0.629, y1:0.392045, x2:0, y2:1, stop:0.696682 rgba(44, 64, 174, 255), stop:0.981043 rgba(255, 255, 255, 255));\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_7.addWidget(self.numero_pv, 1, 0, 1, 2)

        self.numero_reg = QLineEdit(self.tab)
        self.numero_reg.setObjectName(u"numero_reg")
        self.numero_reg.setEnabled(False)
        self.numero_reg.setFont(font4)
        self.numero_reg.setStyleSheet(u"background-color: qlineargradient(spread:reflect, x1:0.629, y1:0.392045, x2:0, y2:1, stop:0.696682 rgba(44, 64, 174, 255), stop:0.981043 rgba(255, 255, 255, 255));\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_7.addWidget(self.numero_reg, 0, 0, 1, 2)

        self.label_7 = QLabel(self.tab)
        self.label_7.setObjectName(u"label_7")
        self.label_7.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_7.addWidget(self.label_7, 2, 0, 1, 2)

        self.label_10 = QLabel(self.tab)
        self.label_10.setObjectName(u"label_10")
        self.label_10.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_7.addWidget(self.label_10, 12, 0, 1, 2)

        self.annee = QComboBox(self.tab)
        self.annee.setObjectName(u"annee")
        self.annee.setFont(font3)
        self.annee.setLayoutDirection(Qt.RightToLeft)
        self.annee.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_7.addWidget(self.annee, 11, 0, 1, 2)

        self.date_pv = QDateEdit(self.tab)
        self.date_pv.setObjectName(u"date_pv")
        self.date_pv.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_pv.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.date_pv.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.date_pv.setCalendarPopup(True)

        self.gridLayout_7.addWidget(self.date_pv, 3, 0, 1, 2)

        self.date_envoi_jus = QDateEdit(self.tab)
        self.date_envoi_jus.setObjectName(u"date_envoi_jus")
        self.date_envoi_jus.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_envoi_jus.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.date_envoi_jus.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.date_envoi_jus.setCalendarPopup(True)

        self.gridLayout_7.addWidget(self.date_envoi_jus, 8, 0, 1, 2)

        self.label_9 = QLabel(self.tab)
        self.label_9.setObjectName(u"label_9")
        self.label_9.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_7.addWidget(self.label_9, 6, 0, 1, 2)

        self.date_const = QDateEdit(self.tab)
        self.date_const.setObjectName(u"date_const")
        self.date_const.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_const.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.date_const.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.date_const.setCalendarPopup(True)

        self.gridLayout_7.addWidget(self.date_const, 5, 0, 1, 2)

        self.date_audience = QDateEdit(self.tab)
        self.date_audience.setObjectName(u"date_audience")
        self.date_audience.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_audience.setButtonSymbols(QAbstractSpinBox.UpDownArrows)
        self.date_audience.setDateTime(QDateTime(QDate(2023, 1, 3), QTime(8, 0, 0)))
        self.date_audience.setCalendarPopup(True)

        self.gridLayout_7.addWidget(self.date_audience, 13, 0, 1, 2)

        self.btn_pv = QPushButton(self.tab)
        self.btn_pv.setObjectName(u"btn_pv")
        self.btn_pv.setStyleSheet(u"QPushButton{\n"
"            border-style:outset;\n"
"            border-with : 2px;\n"
"            border-radius:10px;\n"
"            border-color: beige;\n"
"            font:bold 14px;\n"
"            min-with : 10 em;\n"
"            padding : 6px;\n"
"            transition: 0.5s;\n"
"            }\n"
"            QPushButton::hover{\n"
"            background-color:#1aff6e;\n"
"            letter-spacing: 1px;\n"
"            }")

        self.gridLayout_7.addWidget(self.btn_pv, 9, 0, 1, 2)

        self.tabWidget_2.addTab(self.tab, "")
        self.tab_3 = QWidget()
        self.tab_3.setObjectName(u"tab_3")
        self.gridLayout_9 = QGridLayout(self.tab_3)
        self.gridLayout_9.setObjectName(u"gridLayout_9")
        self.gridLayout_10 = QGridLayout()
        self.gridLayout_10.setObjectName(u"gridLayout_10")
        self.label_12 = QLabel(self.tab_3)
        self.label_12.setObjectName(u"label_12")
        self.label_12.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_10.addWidget(self.label_12, 1, 0, 1, 1)

        self.montant_recue = QLineEdit(self.tab_3)
        self.montant_recue.setObjectName(u"montant_recue")
        self.montant_recue.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_10.addWidget(self.montant_recue, 6, 0, 1, 1)

        self.date_recue = QDateEdit(self.tab_3)
        self.date_recue.setObjectName(u"date_recue")
        self.date_recue.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_recue.setDateTime(QDateTime(QDate(2023, 1, 1), QTime(7, 0, 0)))
        self.date_recue.setCalendarPopup(True)

        self.gridLayout_10.addWidget(self.date_recue, 4, 0, 1, 1)

        self.numero_recue = QLineEdit(self.tab_3)
        self.numero_recue.setObjectName(u"numero_recue")
        self.numero_recue.setStyleSheet(u"font: 700 12pt \"Segoe UI\";\n"
"color: rgb(255, 255, 255);")

        self.gridLayout_10.addWidget(self.numero_recue, 0, 0, 1, 1)

        self.date_avert = QDateEdit(self.tab_3)
        self.date_avert.setObjectName(u"date_avert")
        self.date_avert.setStyleSheet(u"background-color: rgb(233, 254, 255);\n"
"color: rgb(84, 84, 84);")
        self.date_avert.setDateTime(QDateTime(QDate(2023, 1, 1), QTime(7, 0, 0)))
        self.date_avert.setCalendarPopup(True)

        self.gridLayout_10.addWidget(self.date_avert, 2, 0, 1, 1)

        self.label_13 = QLabel(self.tab_3)
        self.label_13.setObjectName(u"label_13")
        self.label_13.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_10.addWidget(self.label_13, 3, 0, 1, 1)


        self.gridLayout_9.addLayout(self.gridLayout_10, 0, 0, 1, 1)

        self.verticalLayout_7 = QVBoxLayout()
        self.verticalLayout_7.setObjectName(u"verticalLayout_7")
        self.tableWidget_avert = QTableWidget(self.tab_3)
        if (self.tableWidget_avert.columnCount() < 5):
            self.tableWidget_avert.setColumnCount(5)
        __qtablewidgetitem13 = QTableWidgetItem()
        __qtablewidgetitem13.setFont(font2);
        self.tableWidget_avert.setHorizontalHeaderItem(0, __qtablewidgetitem13)
        __qtablewidgetitem14 = QTableWidgetItem()
        __qtablewidgetitem14.setFont(font2);
        self.tableWidget_avert.setHorizontalHeaderItem(1, __qtablewidgetitem14)
        __qtablewidgetitem15 = QTableWidgetItem()
        __qtablewidgetitem15.setFont(font2);
        self.tableWidget_avert.setHorizontalHeaderItem(2, __qtablewidgetitem15)
        __qtablewidgetitem16 = QTableWidgetItem()
        __qtablewidgetitem16.setFont(font2);
        self.tableWidget_avert.setHorizontalHeaderItem(3, __qtablewidgetitem16)
        __qtablewidgetitem17 = QTableWidgetItem()
        __qtablewidgetitem17.setFont(font2);
        self.tableWidget_avert.setHorizontalHeaderItem(4, __qtablewidgetitem17)
        self.tableWidget_avert.setObjectName(u"tableWidget_avert")
        self.tableWidget_avert.setSelectionBehavior(QAbstractItemView.SelectRows)

        self.verticalLayout_7.addWidget(self.tableWidget_avert)


        self.gridLayout_9.addLayout(self.verticalLayout_7, 1, 0, 1, 1)

        self.tabWidget_2.addTab(self.tab_3, "")

        self.gridLayout_4.addWidget(self.tabWidget_2, 0, 0, 5, 1)

        self.combox_com = QComboBox(self.centralwidget)
        self.combox_com.addItem("")
        self.combox_com.addItem("")
        self.combox_com.addItem("")
        self.combox_com.setObjectName(u"combox_com")
        self.combox_com.setLayoutDirection(Qt.RightToLeft)
        self.combox_com.setStyleSheet(u"font: 700 10pt \"Segoe UI\";\n"
"")

        self.gridLayout_4.addWidget(self.combox_com, 3, 1, 1, 1)

        self.reche_com = QLineEdit(self.centralwidget)
        self.reche_com.setObjectName(u"reche_com")
        self.reche_com.setLayoutDirection(Qt.RightToLeft)
        self.reche_com.setStyleSheet(u"font: 700 12pt \"Segoe UI\";")

        self.gridLayout_4.addWidget(self.reche_com, 4, 1, 1, 1)

        self.tabWidget = QTabWidget(self.centralwidget)
        self.tabWidget.setObjectName(u"tabWidget")
        font5 = QFont()
        font5.setFamilies([u"Segoe UI"])
        font5.setPointSize(12)
        font5.setBold(True)
        self.tabWidget.setFont(font5)
        self.tabWidget.setLayoutDirection(Qt.RightToLeft)
        self.tabWidget.setStyleSheet(u"background-color: qlineargradient(spread:pad, x1:0.426136, y1:0.159091, x2:1, y2:1, stop:0.142045 rgba(58, 196, 255, 255), stop:1 rgba(255, 255, 255, 255));")
        self.tabWidget.setTabPosition(QTabWidget.North)
        self.tabWidget.setTabShape(QTabWidget.Triangular)
        self.tabWidget.setElideMode(Qt.ElideLeft)
        self.tabWidget.setUsesScrollButtons(True)
        self.tabWidget.setDocumentMode(True)
        self.tabWidget.setTabsClosable(False)
        self.tabWidget.setMovable(True)
        self.tabWidget.setTabBarAutoHide(True)
        self.Aceule = QWidget()
        self.Aceule.setObjectName(u"Aceule")
        font6 = QFont()
        font6.setStrikeOut(False)
        self.Aceule.setFont(font6)
        self.verticalLayout = QVBoxLayout(self.Aceule)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.frame_4 = QFrame(self.Aceule)
        self.frame_4.setObjectName(u"frame_4")
        self.frame_4.setFrameShape(QFrame.StyledPanel)
        self.frame_4.setFrameShadow(QFrame.Raised)
        self.verticalLayout_2 = QVBoxLayout(self.frame_4)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.label_5 = QLabel(self.frame_4)
        self.label_5.setObjectName(u"label_5")
        self.label_5.setMaximumSize(QSize(16777215, 40))
        font7 = QFont()
        font7.setPointSize(14)
        font7.setBold(True)
        font7.setItalic(True)
        self.label_5.setFont(font7)
        self.label_5.setAlignment(Qt.AlignCenter)

        self.verticalLayout_2.addWidget(self.label_5)

        self.label_6 = QLabel(self.frame_4)
        self.label_6.setObjectName(u"label_6")
        self.label_6.setMaximumSize(QSize(16777215, 40))
        self.label_6.setFont(font7)
        self.label_6.setAlignment(Qt.AlignCenter)

        self.verticalLayout_2.addWidget(self.label_6)

        self.label_2 = QLabel(self.frame_4)
        self.label_2.setObjectName(u"label_2")
        self.label_2.setMaximumSize(QSize(16777215, 40))
        self.label_2.setFont(font7)
        self.label_2.setLayoutDirection(Qt.RightToLeft)
        self.label_2.setAutoFillBackground(False)
        self.label_2.setAlignment(Qt.AlignCenter)

        self.verticalLayout_2.addWidget(self.label_2)

        self.label_3 = QLabel(self.frame_4)
        self.label_3.setObjectName(u"label_3")
        self.label_3.setStyleSheet(u"image: url(:/image/img/siege.jpg);")

        self.verticalLayout_2.addWidget(self.label_3)


        self.verticalLayout.addWidget(self.frame_4)

        icon18 = QIcon()
        icon18.addFile(u":/image/img/home_automation_50px.png", QSize(), QIcon.Normal, QIcon.Off)
        self.tabWidget.addTab(self.Aceule, icon18, "")
        self.commercent = QWidget()
        self.commercent.setObjectName(u"commercent")
        self.gridLayout_8 = QGridLayout(self.commercent)
        self.gridLayout_8.setObjectName(u"gridLayout_8")
        self.verticalLayout_4 = QVBoxLayout()
        self.verticalLayout_4.setObjectName(u"verticalLayout_4")
        self.lcdNumber = QLCDNumber(self.commercent)
        self.lcdNumber.setObjectName(u"lcdNumber")

        self.verticalLayout_4.addWidget(self.lcdNumber)

        self.label_11 = QLabel(self.commercent)
        self.label_11.setObjectName(u"label_11")
        self.label_11.setSizeIncrement(QSize(0, 0))
        self.label_11.setStyleSheet(u"color: rgb(0, 0, 0);\n"
"font: 700 18pt \"Segoe UI\";")
        self.label_11.setAlignment(Qt.AlignCenter)

        self.verticalLayout_4.addWidget(self.label_11)

        self.lcdNumber_2 = QLCDNumber(self.commercent)
        self.lcdNumber_2.setObjectName(u"lcdNumber_2")

        self.verticalLayout_4.addWidget(self.lcdNumber_2)


        self.gridLayout_8.addLayout(self.verticalLayout_4, 0, 1, 1, 1)

        self.horizontalSpacer_2 = QSpacerItem(100, 20, QSizePolicy.Preferred, QSizePolicy.Minimum)

        self.gridLayout_8.addItem(self.horizontalSpacer_2, 0, 0, 1, 1)

        self.horizontalSpacer = QSpacerItem(100, 20, QSizePolicy.Preferred, QSizePolicy.Minimum)

        self.gridLayout_8.addItem(self.horizontalSpacer, 0, 2, 1, 1)

        icon19 = QIcon()
        icon19.addFile(u":/image/img/icons8_add_user_male.ico", QSize(), QIcon.Normal, QIcon.Off)
        self.tabWidget.addTab(self.commercent, icon19, "")
        self.actevite = QWidget()
        self.actevite.setObjectName(u"actevite")
        self.gridLayout_6 = QGridLayout(self.actevite)
        self.gridLayout_6.setObjectName(u"gridLayout_6")
        self.tableWidget_act = QTableWidget(self.actevite)
        if (self.tableWidget_act.columnCount() < 3):
            self.tableWidget_act.setColumnCount(3)
        __qtablewidgetitem18 = QTableWidgetItem()
        __qtablewidgetitem18.setFont(font1);
        self.tableWidget_act.setHorizontalHeaderItem(0, __qtablewidgetitem18)
        __qtablewidgetitem19 = QTableWidgetItem()
        __qtablewidgetitem19.setFont(font1);
        self.tableWidget_act.setHorizontalHeaderItem(1, __qtablewidgetitem19)
        icon20 = QIcon()
        icon20.addFile(u":/img/img/house.png", QSize(), QIcon.Normal, QIcon.Off)
        __qtablewidgetitem20 = QTableWidgetItem()
        __qtablewidgetitem20.setFont(font1);
        __qtablewidgetitem20.setIcon(icon20);
        self.tableWidget_act.setHorizontalHeaderItem(2, __qtablewidgetitem20)
        self.tableWidget_act.setObjectName(u"tableWidget_act")
        font8 = QFont()
        font8.setPointSize(12)
        self.tableWidget_act.setFont(font8)
        self.tableWidget_act.setSelectionBehavior(QAbstractItemView.SelectRows)

        self.gridLayout_6.addWidget(self.tableWidget_act, 0, 0, 1, 1)

        self.rech_act = QLineEdit(self.actevite)
        self.rech_act.setObjectName(u"rech_act")

        self.gridLayout_6.addWidget(self.rech_act, 1, 0, 1, 1)

        self.tabWidget.addTab(self.actevite, icon2, "")
        self.tab_2 = QWidget()
        self.tab_2.setObjectName(u"tab_2")
        self.gridLayout_5 = QGridLayout(self.tab_2)
        self.gridLayout_5.setObjectName(u"gridLayout_5")
        self.tableWidget_pv = QTableWidget(self.tab_2)
        if (self.tableWidget_pv.columnCount() < 8):
            self.tableWidget_pv.setColumnCount(8)
        __qtablewidgetitem21 = QTableWidgetItem()
        __qtablewidgetitem21.setFont(font1);
        self.tableWidget_pv.setHorizontalHeaderItem(0, __qtablewidgetitem21)
        __qtablewidgetitem22 = QTableWidgetItem()
        __qtablewidgetitem22.setFont(font1);
        self.tableWidget_pv.setHorizontalHeaderItem(1, __qtablewidgetitem22)
        __qtablewidgetitem23 = QTableWidgetItem()
        __qtablewidgetitem23.setFont(font1);
        __qtablewidgetitem23.setIcon(icon20);
        self.tableWidget_pv.setHorizontalHeaderItem(2, __qtablewidgetitem23)
        font9 = QFont()
        font9.setPointSize(9)
        font9.setBold(True)
        __qtablewidgetitem24 = QTableWidgetItem()
        __qtablewidgetitem24.setFont(font9);
        self.tableWidget_pv.setHorizontalHeaderItem(3, __qtablewidgetitem24)
        __qtablewidgetitem25 = QTableWidgetItem()
        __qtablewidgetitem25.setFont(font9);
        self.tableWidget_pv.setHorizontalHeaderItem(4, __qtablewidgetitem25)
        __qtablewidgetitem26 = QTableWidgetItem()
        __qtablewidgetitem26.setFont(font9);
        self.tableWidget_pv.setHorizontalHeaderItem(5, __qtablewidgetitem26)
        __qtablewidgetitem27 = QTableWidgetItem()
        __qtablewidgetitem27.setFont(font9);
        self.tableWidget_pv.setHorizontalHeaderItem(6, __qtablewidgetitem27)
        __qtablewidgetitem28 = QTableWidgetItem()
        __qtablewidgetitem28.setFont(font9);
        self.tableWidget_pv.setHorizontalHeaderItem(7, __qtablewidgetitem28)
        self.tableWidget_pv.setObjectName(u"tableWidget_pv")
        self.tableWidget_pv.setFont(font8)
        self.tableWidget_pv.setSelectionBehavior(QAbstractItemView.SelectRows)

        self.gridLayout_5.addWidget(self.tableWidget_pv, 0, 0, 1, 1)

        self.rech_com_pv = QLineEdit(self.tab_2)
        self.rech_com_pv.setObjectName(u"rech_com_pv")

        self.gridLayout_5.addWidget(self.rech_com_pv, 1, 0, 1, 1)

        icon21 = QIcon()
        icon21.addFile(u":/image/img/icons8_briefcase.ico", QSize(), QIcon.Normal, QIcon.Off)
        self.tabWidget.addTab(self.tab_2, icon21, "")

        self.gridLayout_4.addWidget(self.tabWidget, 0, 1, 1, 1)

        MainWindow.setCentralWidget(self.centralwidget)
        self.menuBar = QMenuBar(MainWindow)
        self.menuBar.setObjectName(u"menuBar")
        self.menuBar.setGeometry(QRect(0, 0, 978, 22))
        font10 = QFont()
        font10.setFamilies([u"Tahoma"])
        font10.setPointSize(10)
        self.menuBar.setFont(font10)
        self.menuView = QMenu(self.menuBar)
        self.menuView.setObjectName(u"menuView")
        self.menuTable = QMenu(self.menuBar)
        self.menuTable.setObjectName(u"menuTable")
        MainWindow.setMenuBar(self.menuBar)
        self.toolBar = QToolBar(MainWindow)
        self.toolBar.setObjectName(u"toolBar")
        MainWindow.addToolBar(Qt.LeftToolBarArea, self.toolBar)

        self.menuBar.addAction(self.menuView.menuAction())
        self.menuBar.addAction(self.menuTable.menuAction())
        self.menuView.addSeparator()
        self.menuView.addAction(self.actioncommercent)
        self.menuView.addSeparator()
        self.menuView.addAction(self.actionactevite)
        self.menuView.addSeparator()
        self.menuView.addAction(self.actionPv)
        self.menuView.addAction(self.actionAfaire)
        self.menuView.addSeparator()
        self.menuTable.addAction(self.actiontable_Activite)
        self.menuTable.addAction(self.actiontable_Infraction)
        self.toolBar.addAction(self.actionNouveau)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionAjouter)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionModifier)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionSuprimer)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionImprimer)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionSave)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionHide)
        self.toolBar.addAction(self.actionShow)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionNext)
        self.toolBar.addAction(self.actionReturn)
        self.toolBar.addSeparator()
        self.toolBar.addAction(self.actionClose)

        self.retranslateUi(MainWindow)
        # self.actionAjouter.triggered.connect(self.actionAjouter.trigger)
        # self.actionModifier.triggered.connect(self.actionModifier.trigger)
        # self.actionSuprimer.triggered.connect(self.actionSuprimer.trigger)
        # self.actionImprimer.triggered.connect(self.actionImprimer.trigger)
        # self.actionNouveau.triggered.connect(self.actionNouveau.trigger)
        # self.actionSave.triggered.connect(self.actionSave.trigger)
        # self.actionClose.triggered.connect(MainWindow.close)
        # self.actionactevite.triggered.connect(self.actionactevite.trigger)
        # self.actioninfraction.triggered.connect(self.actioninfraction.trigger)
        # self.reche_com.textChanged.connect(self.reche_com.clear)
        # self.actiontable_Activite.triggered.connect(self.actiontable_Activite.trigger)
        # self.actionShow.triggered.connect(self.tabWidget_2.show)
        # self.actionHide.triggered.connect(self.tabWidget_2.hide)
        # self.actionNext.triggered.connect(self.actionNext.trigger)
        # self.actionReturn.triggered.connect(self.actionReturn.trigger)
        # self.tabWidget_2.currentChanged.connect(self.tabWidget.setCurrentIndex)
        # self.rech_act.textChanged.connect(self.rech_act.clear)
        # self.actioncommercent.triggered.connect(self.tabWidget.show)
        # self.tableWidget_act.currentCellChanged.connect(self.tableWidget_act.clear)
        # self.tabWidget.tabCloseRequested.connect(self.tabWidget.close)

        self.tabWidget_2.setCurrentIndex(0)
        self.tabWidget.setCurrentIndex(0)


        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u062a\u0637\u0628\u064a\u0642 \u0623\u0631\u0634\u064a\u0641 \u0627\u0644\u0645\u0646\u0627\u0632\u0639\u0627\u062a", None))
        self.actioncommercent.setText(QCoreApplication.translate("MainWindow", u"commercent", None))
        self.actionactevite.setText(QCoreApplication.translate("MainWindow", u"actevite", None))
        self.actioninfraction.setText(QCoreApplication.translate("MainWindow", u"infraction", None))
        self.actionAjouter.setText(QCoreApplication.translate("MainWindow", u"Ajouter", None))
#if QT_CONFIG(shortcut)
        self.actionAjouter.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+A", None))
#endif // QT_CONFIG(shortcut)
        self.actionModifier.setText(QCoreApplication.translate("MainWindow", u"Modifier", None))
#if QT_CONFIG(shortcut)
        self.actionModifier.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+M", None))
#endif // QT_CONFIG(shortcut)
        self.actionSuprimer.setText(QCoreApplication.translate("MainWindow", u"Suprimer", None))
#if QT_CONFIG(shortcut)
        self.actionSuprimer.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+S", None))
#endif // QT_CONFIG(shortcut)
        self.actionImprimer.setText(QCoreApplication.translate("MainWindow", u"Imprimer", None))
#if QT_CONFIG(shortcut)
        self.actionImprimer.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+P", None))
#endif // QT_CONFIG(shortcut)
        self.actionClose.setText(QCoreApplication.translate("MainWindow", u"Close", None))
#if QT_CONFIG(shortcut)
        self.actionClose.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+F", None))
#endif // QT_CONFIG(shortcut)
        self.actionSave.setText(QCoreApplication.translate("MainWindow", u"Save", None))
#if QT_CONFIG(shortcut)
        self.actionSave.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+S", None))
#endif // QT_CONFIG(shortcut)
        self.actionNouveau.setText(QCoreApplication.translate("MainWindow", u"Nouveau", None))
#if QT_CONFIG(shortcut)
        self.actionNouveau.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+N", None))
#endif // QT_CONFIG(shortcut)
        self.actionHide.setText(QCoreApplication.translate("MainWindow", u"Hide", None))
#if QT_CONFIG(shortcut)
        self.actionHide.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+H", None))
#endif // QT_CONFIG(shortcut)
        self.actionShow.setText(QCoreApplication.translate("MainWindow", u"Show", None))
#if QT_CONFIG(shortcut)
        self.actionShow.setShortcut(QCoreApplication.translate("MainWindow", u"Ctrl+S", None))
#endif // QT_CONFIG(shortcut)
        self.actiontable_Activite.setText(QCoreApplication.translate("MainWindow", u"table Activite", None))
        self.actionNext.setText(QCoreApplication.translate("MainWindow", u"Next", None))
        self.actionReturn.setText(QCoreApplication.translate("MainWindow", u"Return", None))
        self.actiontable_Infraction.setText(QCoreApplication.translate("MainWindow", u"table Infraction", None))
        self.actionPv.setText(QCoreApplication.translate("MainWindow", u"Pv", None))
        self.actionAfaire.setText(QCoreApplication.translate("MainWindow", u"Afaire", None))
        ___qtablewidgetitem = self.tableWidget_com.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"id", None));
        ___qtablewidgetitem1 = self.tableWidget_com.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"\u0631\u0642\u0645 \u0627\u0644\u0633\u062c\u0644", None));
        ___qtablewidgetitem2 = self.tableWidget_com.horizontalHeaderItem(2)
        ___qtablewidgetitem2.setText(QCoreApplication.translate("MainWindow", u"\u0644\u0642\u0628 \u0627\u0644\u062a\u0627\u062c\u0631", None));
        ___qtablewidgetitem3 = self.tableWidget_com.horizontalHeaderItem(3)
        ___qtablewidgetitem3.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u062a\u0627\u062c\u0631", None));
        ___qtablewidgetitem4 = self.tableWidget_com.horizontalHeaderItem(4)
        ___qtablewidgetitem4.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0645\u064a\u0644\u0627\u0630", None));
        ___qtablewidgetitem5 = self.tableWidget_com.horizontalHeaderItem(5)
        ___qtablewidgetitem5.setText(QCoreApplication.translate("MainWindow", u"\u0645\u0643\u0627\u0646 \u0627\u0644\u0645\u064a\u0644\u0627\u0630", None));
        ___qtablewidgetitem6 = self.tableWidget_com.horizontalHeaderItem(6)
        ___qtablewidgetitem6.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0623\u0628", None));
        ___qtablewidgetitem7 = self.tableWidget_com.horizontalHeaderItem(7)
        ___qtablewidgetitem7.setText(QCoreApplication.translate("MainWindow", u"\u0644\u0642\u0628 \u0627\u0644\u0623\u0645", None));
        ___qtablewidgetitem8 = self.tableWidget_com.horizontalHeaderItem(8)
        ___qtablewidgetitem8.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0623\u0645", None));
        ___qtablewidgetitem9 = self.tableWidget_com.horizontalHeaderItem(9)
        ___qtablewidgetitem9.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0639\u0646\u0648\u0627\u0646", None));
        ___qtablewidgetitem10 = self.tableWidget_com.horizontalHeaderItem(10)
        ___qtablewidgetitem10.setText(QCoreApplication.translate("MainWindow", u"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0645\u062d\u0644", None));
        ___qtablewidgetitem11 = self.tableWidget_com.horizontalHeaderItem(11)
        ___qtablewidgetitem11.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0633\u062c\u0644", None));
        ___qtablewidgetitem12 = self.tableWidget_com.horizontalHeaderItem(12)
        ___qtablewidgetitem12.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u062d\u0627\u0644\u0629", None));
        self.reg_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0631\u0642\u0645 \u0627\u0644\u0633\u062c\u0644", None))
        self.nom_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0644\u0642\u0628 \u0627\u0644\u062a\u0627\u062c\u0631", None))
        self.prenom_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u062a\u0627\u062c\u0631", None))
        self.label.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0645\u064a\u0644\u0627\u062f :", None))
        self.lieu_nai_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0645\u0643\u0627\u0646 \u0627\u0644\u0645\u064a\u0644\u0627\u0630", None))
        self.prenomPer_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0623\u0628", None))
        self.nomMe_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0644\u0642\u0628 \u0627\u0644\u0623\u0645", None))
        self.prenomMer_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0623\u0645", None))
        self.Adresse_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0639\u0646\u0648\u0627\u0646", None))
        self.adresseLoc_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0639\u0646\u0648\u0627\u0646 \u0627\u0644\u0645\u062d\u0644", None))
        self.label_4.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0633\u062c\u0644 :", None))
        self.setuation_com.setItemText(0, QCoreApplication.translate("MainWindow", u"\u062d\u0627\u0636\u0631", None))
        self.setuation_com.setItemText(1, QCoreApplication.translate("MainWindow", u"\u063a\u0627\u0626\u0628", None))

        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.ajou_com), QCoreApplication.translate("MainWindow", u"\u0627\u0636\u0627\u0641\u0629 \u062a\u0627\u062c\u0631", None))
        self.code_actevite.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0631\u0645\u0632 \u0627\u0644\u0646\u0634\u0627\u0637", None))
        self.comboBox_act.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0646\u0634\u0627\u0637", None))
        self.Button_actuelise.setText(QCoreApplication.translate("MainWindow", u"\u062a\u062d\u0630\u064a\u062b", None))
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.ajou_act), QCoreApplication.translate("MainWindow", u"\u0627\u0636\u0627\u0641\u0629 \u0646\u0634\u0627\u0637", None))
        self.label_8.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u062a\u0627\u0643\u064a\u062f", None))
        self.infraction_pv.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0645\u062e\u0627\u0644\u0641\u0629", None))
        self.numero_pv.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0631\u0642\u0645 \u0627\u0644\u0645\u062d\u0636\u0631", None))
        self.numero_reg.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0631\u0642\u0645 \u0627\u0644\u0633\u062c\u0644", None))
        self.label_7.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0645\u062d\u0636\u0631", None))
        self.label_10.setText(QCoreApplication.translate("MainWindow", u"date audionce", None))
        self.annee.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0633\u0646\u0629", None))
        self.label_9.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0631\u0633\u0627\u0644 \u0627\u0644\u0649 \u0627\u0644\u062d\u0643\u0645\u0629", None))
        self.btn_pv.setText(QCoreApplication.translate("MainWindow", u"\u062a\u062d\u0630\u064a\u062b", None))
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab), QCoreApplication.translate("MainWindow", u"\u0627\u0636\u0627\u0641\u0629 \u0645\u062d\u0636\u0631", None))
        self.label_12.setText(QCoreApplication.translate("MainWindow", u" Date Avertismet", None))
        self.montant_recue.setPlaceholderText(QCoreApplication.translate("MainWindow", u"Montant", None))
        self.numero_recue.setPlaceholderText(QCoreApplication.translate("MainWindow", u"Numero re\u00e7ue", None))
        self.label_13.setText(QCoreApplication.translate("MainWindow", u"date_re\u00e7ue", None))
        ___qtablewidgetitem13 = self.tableWidget_avert.horizontalHeaderItem(0)
        ___qtablewidgetitem13.setText(QCoreApplication.translate("MainWindow", u"id", None));
        ___qtablewidgetitem14 = self.tableWidget_avert.horizontalHeaderItem(1)
        ___qtablewidgetitem14.setText(QCoreApplication.translate("MainWindow", u"Numero recue", None));
        ___qtablewidgetitem15 = self.tableWidget_avert.horizontalHeaderItem(2)
        ___qtablewidgetitem15.setText(QCoreApplication.translate("MainWindow", u"Date_avertisment", None));
        ___qtablewidgetitem16 = self.tableWidget_avert.horizontalHeaderItem(3)
        ___qtablewidgetitem16.setText(QCoreApplication.translate("MainWindow", u"Date  recue", None));
        ___qtablewidgetitem17 = self.tableWidget_avert.horizontalHeaderItem(4)
        ___qtablewidgetitem17.setText(QCoreApplication.translate("MainWindow", u"Montant", None));
        self.tabWidget_2.setTabText(self.tabWidget_2.indexOf(self.tab_3), QCoreApplication.translate("MainWindow", u"\u0627\u0636\u0627\u0641\u0629 \u0639\u0645\u0644\u064a\u0629", None))
        self.combox_com.setItemText(0, QCoreApplication.translate("MainWindow", u"\u0644\u0642\u0628 \u0627\u0644\u062a\u0627\u062c\u0631", None))
        self.combox_com.setItemText(1, QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u062a\u0627\u062c\u0631", None))
        self.combox_com.setItemText(2, QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0633\u062c\u0644 \u0627\u0644\u062a\u062c\u0627\u0631\u064a", None))

        self.reche_com.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0628\u062d\u062b \u0633\u0631\u064a\u0639", None))
        self.label_5.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u062c\u0645\u0647\u0648\u0631\u064a\u0629 \u0627\u0644\u062c\u0632\u0627\u0626\u0631\u064a\u0629 \u0627\u0644\u062f\u064a\u0645\u0642\u0631\u0627\u0637\u064a\u0629 \u0627\u0644\u0634\u0639\u0628\u064a\u0629", None))
        self.label_6.setText(QCoreApplication.translate("MainWindow", u"\u0648\u0632\u0627\u0631\u0629 \u0627\u0644\u062a\u062c\u0627\u0631\u0629 \u0648 \u062a\u0631\u0642\u064a\u0629 \u0627\u0644\u0635\u0627\u062f\u0631\u0627\u062a", None))
        self.label_2.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0645\u062f\u064a\u0631\u064a\u0629 \u0627\u0644\u0648\u0644\u0627\u0626\u064a\u0629 \u0644\u0644\u062a\u062c\u0627\u0631\u0629 \u0648 \u062a\u0631\u0642\u064a\u0629 \u0627\u0644\u0635\u0627\u062f\u0631\u0627\u062a \u0628\u0634\u0627\u0631", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.Aceule), QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0631\u0626\u064a\u0633\u064a\u0629", None))
        self.label_11.setText(QCoreApplication.translate("MainWindow", u"\u0639\u062f\u062f \u0627\u0644\u062a\u062c\u0627\u0631 ", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.commercent), QCoreApplication.translate("MainWindow", u"\u0627\u0644\u062a\u062c\u0627\u0631", None))
        ___qtablewidgetitem18 = self.tableWidget_act.horizontalHeaderItem(0)
        ___qtablewidgetitem18.setText(QCoreApplication.translate("MainWindow", u"id", None));
        ___qtablewidgetitem19 = self.tableWidget_act.horizontalHeaderItem(1)
        ___qtablewidgetitem19.setText(QCoreApplication.translate("MainWindow", u"\u0631\u0645\u0632 \u0627\u0644\u0646\u0634\u0627\u0637", None));
        ___qtablewidgetitem20 = self.tableWidget_act.horizontalHeaderItem(2)
        ___qtablewidgetitem20.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0627\u0646\u0634\u0627\u0637", None));
        self.rech_act.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0628\u062d\u062b \u0633\u0631\u064a\u0639", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.actevite), QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0627\u0646\u0634\u0637\u0629 ", None))
        ___qtablewidgetitem21 = self.tableWidget_pv.horizontalHeaderItem(0)
        ___qtablewidgetitem21.setText(QCoreApplication.translate("MainWindow", u"id", None));
        ___qtablewidgetitem22 = self.tableWidget_pv.horizontalHeaderItem(1)
        ___qtablewidgetitem22.setText(QCoreApplication.translate("MainWindow", u"\u0631\u0642\u0645 \u0627\u0644\u0645\u062d\u0636\u0631", None));
        ___qtablewidgetitem23 = self.tableWidget_pv.horizontalHeaderItem(2)
        ___qtablewidgetitem23.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0645\u062d\u0636\u0631", None));
        ___qtablewidgetitem24 = self.tableWidget_pv.horizontalHeaderItem(3)
        ___qtablewidgetitem24.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u062a\u0627\u0643\u064a\u062f", None));
        ___qtablewidgetitem25 = self.tableWidget_pv.horizontalHeaderItem(4)
        ___qtablewidgetitem25.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0627\u0631\u0633\u0627\u0644 \u0627\u0644\u0649 \u0627\u0644\u0645\u062d\u0643\u0645\u0629", None));
        ___qtablewidgetitem26 = self.tableWidget_pv.horizontalHeaderItem(5)
        ___qtablewidgetitem26.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0645\u062e\u0627\u0644\u0641\u0629", None));
        ___qtablewidgetitem27 = self.tableWidget_pv.horizontalHeaderItem(6)
        ___qtablewidgetitem27.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0633\u0646\u0629", None));
        ___qtablewidgetitem28 = self.tableWidget_pv.horizontalHeaderItem(7)
        ___qtablewidgetitem28.setText(QCoreApplication.translate("MainWindow", u"date audionce", None));
        self.rech_com_pv.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0628\u062d\u062b \u0633\u0631\u064a\u0639", None))
        self.tabWidget.setTabText(self.tabWidget.indexOf(self.tab_2), QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0645\u062d\u0627\u0636\u0631", None))
        self.menuView.setTitle(QCoreApplication.translate("MainWindow", u"View", None))
        self.menuTable.setTitle(QCoreApplication.translate("MainWindow", u"Table", None))
        self.toolBar.setWindowTitle(QCoreApplication.translate("MainWindow", u"toolBar", None))
    # retranslateUi


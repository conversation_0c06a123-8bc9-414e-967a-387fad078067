from PySide6.QtCore import *  # type: ignore
from PySide6.QtGui import *  # type: ignore
from PySide6.QtWidgets import *  # type: ignore
from ui_infraction import Ui_MainWindow
import data
import pyttsx3


class MainWindow(QMainWindow, Ui_MainWindow):
    def __init__(self) -> None:
        QMainWindow.__init__(self)
        self.setupUi(self)
        self.wel = pyttsx3.init()
        self.voices = self.wel.getProperty('voices')
        self.wel.setProperty('voices', self.voices[0].id)
        self.loaddata()
        self.tableWidget.currentCellChanged.connect(self._cellclicked)
        self.pushButton_2.clicked.connect(self._add)
        self.pushButton.clicked.connect(self._mod)
        self.pushButton_3.clicked.connect(self._ondelete)
        self.pushButton_4.clicked.connect(self._save)
        self.lineEdit_2.textChanged.connect(self.serche)

    def message(self, text, audio):
        self.wel.say(audio)
        self.wel.runAndWait()
        reply = QMessageBox.question(self, MainWindow.windowTitle(self), text,
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        return reply

    def loaddata(self):

        acts = data.session.query(
            data.Infraction).order_by(data.Infraction.id)
        self.tableWidget.setRowCount(acts.count())
        tableRow = 0
        for row in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row.Nom_inf))

            tableRow += 1

    @Slot()
    def _cellclicked(self):
        global id
        r = self.tableWidget.currentRow()
        c = self.tableWidget.columnCount()
        cell = []
        for i in range(c):
            it = self.tableWidget.item(r, i)
            itm = QTableWidgetItem(it).text()
            cell.append(itm)
        id = cell[0]
        self.lineEdit.setText(cell[1])

    def _add(self):
        name = self.lineEdit.text()
        if name == '':

            if self.message('لا يجب ان يكون الحقل فارغا', 'Le champ ne doit pas être vide') == QMessageBox.Yes:
                self.lineEdit.setFocus()
        else:
            data.add_Inf(name)
            self.loaddata()
            self.lineEdit.clear()
            self.lineEdit.setFocus()

    def _mod(self):
        name = self.lineEdit.text()
        if name == '':

            if self.message('قم باختيار في الجدول ', 'Faire une sélection dans le tableau') == QMessageBox.Yes:
                self.lineEdit.setFocus()
        else:

            if self.message('هل تريد التعديل فعلا', 'Voulez-vous vraiment éditer ?') == QMessageBox.Yes:
                data.update_Inf(id, name)
                self.lineEdit.clear()
                self.loaddata()

    def _ondelete(self):
        if self.message('هل تريد الحذف فعلا', 'Voulez-vous vraiment supprimer ?') == QMessageBox.Yes:
                if id != '':
                  data.delete_Inf(id)
                  self.loaddata()
                  self.tableWidget.show
                else:
                    self.message('لا يوجد مخالفات لحذفها', "Il n'y a aucune infraction à supprimer")

    def _save(self):

        if self.message('هل تريد حفظ في قاعدة البيانات ',
                        'Voulez-vous enregistrer dans la base de données') == QMessageBox.Yes:
            data.session.commit()

    def serche(self):
        sh = self.lineEdit_2.text()
        acts = data.session.query(data.Infraction).filter(
            data.Infraction.Nom_inf.ilike('%' + sh + '%'))
        self.tableWidget.setRowCount(acts.count())
        tableRow = 0
        for row_ in acts:
            self.tableWidget.setItem(
                tableRow, 0, QTableWidgetItem(str(row_.id)))
            self.tableWidget.setItem(
                tableRow, 1, QTableWidgetItem(row_.Nom_inf))
            tableRow += 1
        if sh == "":
            self.loaddata()


if __name__ == '__main__':
    app = QApplication()
    win = MainWindow()
    win.show()
    app.exec()

# -*- coding: utf-8 -*-

################################################################################
## Form generated from reading UI file 'infractionnbZiDY.ui'
##
## Created by: Qt User Interface Compiler version 6.4.1
##
## WARNING! All changes made in this file will be lost when recompiling UI file!
################################################################################

from PySide6.QtCore import (QCoreApplication, QDate, QDateTime, QLocale,
    QMetaObject, QObject, QPoint, QRect,
    QSize, QTime, QUrl, Qt)
from PySide6.QtGui import (QBrush, QColor, QConicalGradient, QCursor,
    QFont, QFontDatabase, QGradient, QIcon,
    QImage, QKeySequence, QLinearGradient, QPainter,
    QPalette, QPixmap, QRadialGradient, QTransform)
from PySide6.QtWidgets import (QApplication, QHeaderView, QLineEdit, QMainWindow,
    QPushButton, QSizePolicy, QTableWidget, QTableWidgetItem,
    QVBoxLayout, QWidget)
import images_rc

class Ui_MainWindow(object):
    def setupUi(self, MainWindow):
        if not MainWindow.objectName():
            MainWindow.setObjectName(u"MainWindow")
        MainWindow.resize(597, 539)
        icon = QIcon()
        icon.addFile(u":/image/img/icons8_briefcase_32.png", QSize(), QIcon.Normal, QIcon.Off)
        MainWindow.setWindowIcon(icon)
        MainWindow.setStyleSheet(u"QLineEdit{\n"
"             background: #111;\n"
"            border-radius: 5px;\n"
"            background: #111;\n"
"            color : #fff; \n"
"            font-family: system-ui;\n"
"            font-size: 20px;\n"
"        }\n"
"        \n"
"           QPushButton{\n"
"            border-style:outset;\n"
"            border-with : 2px;\n"
"            border-radius:10px;\n"
"            border-color: beige;\n"
"            font:bold 14px;\n"
"            min-with : 10 em;\n"
"            padding : 6px;\n"
"            transition: 0.5s;\n"
"            }\n"
"            QPushButton::hover{\n"
"            background-color:#1aff6e;\n"
"            letter-spacing: 1px;\n"
"            }")
        self.centralwidget = QWidget(MainWindow)
        self.centralwidget.setObjectName(u"centralwidget")
        self.verticalLayout = QVBoxLayout(self.centralwidget)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.tableWidget = QTableWidget(self.centralwidget)
        if (self.tableWidget.columnCount() < 2):
            self.tableWidget.setColumnCount(2)
        __qtablewidgetitem = QTableWidgetItem()
        self.tableWidget.setHorizontalHeaderItem(0, __qtablewidgetitem)
        icon1 = QIcon()
        icon1.addFile(u":/img/img/add.png", QSize(), QIcon.Normal, QIcon.Off)
        brush = QBrush(QColor(0, 0, 0, 255))
        brush.setStyle(Qt.SolidPattern)
        font = QFont()
        font.setPointSize(10)
        font.setBold(False)
        __qtablewidgetitem1 = QTableWidgetItem()
        __qtablewidgetitem1.setTextAlignment(Qt.AlignJustify|Qt.AlignVCenter);
        __qtablewidgetitem1.setFont(font);
        __qtablewidgetitem1.setForeground(brush);
        __qtablewidgetitem1.setIcon(icon1);
        self.tableWidget.setHorizontalHeaderItem(1, __qtablewidgetitem1)
        self.tableWidget.setObjectName(u"tableWidget")
        self.tableWidget.setStyleSheet(u"background-color: qconicalgradient(cx:0, cy:0, angle:135, stop:0 rgba(255, 255, 0, 69), stop:0.375 rgba(255, 255, 0, 69), stop:0.423533 rgba(251, 255, 0, 145), stop:0.45 rgba(247, 255, 0, 208), stop:0.477581 rgba(255, 244, 71, 130), stop:0.518717 rgba(255, 218, 71, 130), stop:0.55 rgba(255, 255, 0, 255), stop:0.57754 rgba(255, 203, 0, 130), stop:0.625 rgba(255, 255, 0, 69), stop:1 rgba(255, 255, 0, 69));")
        self.tableWidget.setAutoScrollMargin(16)
        self.tableWidget.setColumnWidth(1, 500)
        self.tableWidget.setColumnCount(2)
        self.tableWidget.setColumnWidth(0, 0)


        self.verticalLayout.addWidget(self.tableWidget)

        self.lineEdit = QLineEdit(self.centralwidget)
        self.lineEdit.setObjectName(u"lineEdit")
        self.lineEdit.setStyleSheet(u"f")

        self.verticalLayout.addWidget(self.lineEdit)

        self.lineEdit_2 = QLineEdit(self.centralwidget)
        self.lineEdit_2.setObjectName(u"lineEdit_2")
        self.lineEdit_2.setStyleSheet(u"f")

        self.verticalLayout.addWidget(self.lineEdit_2)

        self.pushButton_2 = QPushButton(self.centralwidget)
        self.pushButton_2.setObjectName(u"pushButton_2")
        self.pushButton_2.setStyleSheet(u"")

        self.verticalLayout.addWidget(self.pushButton_2)

        self.pushButton = QPushButton(self.centralwidget)
        self.pushButton.setObjectName(u"pushButton")
        self.pushButton.setCursor(QCursor(Qt.PointingHandCursor))

        self.verticalLayout.addWidget(self.pushButton)

        self.pushButton_3 = QPushButton(self.centralwidget)
        self.pushButton_3.setObjectName(u"pushButton_3")
        self.pushButton_3.setCursor(QCursor(Qt.PointingHandCursor))

        self.verticalLayout.addWidget(self.pushButton_3)

        self.pushButton_4 = QPushButton(self.centralwidget)
        self.pushButton_4.setObjectName(u"pushButton_4")
        self.pushButton_4.setCursor(QCursor(Qt.PointingHandCursor))

        self.verticalLayout.addWidget(self.pushButton_4)

        MainWindow.setCentralWidget(self.centralwidget)

        self.retranslateUi(MainWindow)
        # self.lineEdit.returnPressed.connect(self.lineEdit.clear)
        # self.pushButton.clicked.connect(self.pushButton.click)
        # self.pushButton_2.clicked.connect(self.pushButton_2.click)
        # self.pushButton_3.clicked.connect(self.pushButton_3.click)
        # self.tableWidget.currentCellChanged.connect(self.tableWidget.selectRow)
        # self.pushButton_4.clicked.connect(self.pushButton_4.click)
        # self.lineEdit_2.textChanged.connect(self.lineEdit_2.clear)
        # self.lineEdit_2.returnPressed.connect(self.lineEdit_2.clear)

        QMetaObject.connectSlotsByName(MainWindow)
    # setupUi

    def retranslateUi(self, MainWindow):
        MainWindow.setWindowTitle(QCoreApplication.translate("MainWindow", u"\u0627\u0644\u0645\u062e\u0627\u0641\u0627\u062a \u0627\u0644\u062a\u062c\u0627\u0631\u064a\u0629", None))
        ___qtablewidgetitem = self.tableWidget.horizontalHeaderItem(0)
        ___qtablewidgetitem.setText(QCoreApplication.translate("MainWindow", u"ID", None));
        ___qtablewidgetitem1 = self.tableWidget.horizontalHeaderItem(1)
        ___qtablewidgetitem1.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0645\u062e\u0627\u0644\u0641\u0629", None));
        self.lineEdit.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0627\u0633\u0645 \u0627\u0644\u0645\u062e\u0627\u0644\u0641\u0629", None))
        self.lineEdit_2.setPlaceholderText(QCoreApplication.translate("MainWindow", u"\u0628\u062d\u062b \u0633\u0631\u064a\u0639", None))
        self.pushButton_2.setText(QCoreApplication.translate("MainWindow", u"\u0627\u0636\u0627\u0641\u0629", None))
        self.pushButton.setText(QCoreApplication.translate("MainWindow", u"\u062a\u0639\u062f\u064a\u0644", None))
        self.pushButton_3.setText(QCoreApplication.translate("MainWindow", u"\u062d\u0630\u0641", None))
        self.pushButton_4.setText(QCoreApplication.translate("MainWindow", u"\u062d\u0641\u0638", None))
    # retranslateUi


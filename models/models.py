"""
Database models for SCTX Application
Contains all SQLAlchemy model definitions
"""
from datetime import date
from typing import Optional, List
from sqlalchemy import Column, Integer, String, Date, ForeignKey, Float
from sqlalchemy.orm import declarative_base, relationship
try:
    from sqlalchemy.orm import Mapped
except ImportError:
    # For older SQLAlchemy versions
    Mapped = None

Base = declarative_base()


class Login(Base):
    """User login model"""
    __tablename__ = "login"

    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    password = Column(String(50), nullable=False)
    
    def __init__(self, name: str, password: str):
        self.name = name
        self.password = password
    
    def __repr__(self) -> str:
        return f"<Login(id={self.id}, name='{self.name}')>"


class Commercent(Base):
    """Commercial entity model"""
    __tablename__ = 'commercent'

    id = Column(Integer, primary_key=True)
    Num_reg = Column(String(50), nullable=False, unique=True)
    Nom_com = Column(String(50), nullable=False)
    Prenom_com = Column(String(50), nullable=False)
    Date_nai = Column(Date)
    Lieu_nai = Column(String(50))
    Adresse_dom = Column(String(100))
    Adresse_local = Column(String(100))
    Prenom_per = Column(String(50))
    Nom_mer = Column(String(50))
    Prenom_mer = Column(String(50))
    Date_reg = Column(Date)
    Etat = Column(String(20))

    # Relationships
    activities = relationship("Activite", back_populates="commercent", cascade="all, delete-orphan")
    pvs = relationship("Pv", back_populates="commercent", cascade="all, delete-orphan")
    
    def __init__(self, Num_reg: str, Nom_com: str, Prenom_com: str, 
                 Date_nai: Optional[date] = None, Lieu_nai: Optional[str] = None,
                 Adresse_dom: Optional[str] = None, Adresse_local: Optional[str] = None,
                 Prenom_per: Optional[str] = None, Nom_mer: Optional[str] = None,
                 Prenom_mer: Optional[str] = None, Date_reg: Optional[date] = None,
                 Etat: Optional[str] = None):
        self.Num_reg = Num_reg
        self.Nom_com = Nom_com
        self.Prenom_com = Prenom_com
        self.Date_nai = Date_nai
        self.Lieu_nai = Lieu_nai
        self.Adresse_dom = Adresse_dom
        self.Adresse_local = Adresse_local
        self.Prenom_per = Prenom_per
        self.Nom_mer = Nom_mer
        self.Prenom_mer = Prenom_mer
        self.Date_reg = Date_reg
        self.Etat = Etat
    
    def __repr__(self) -> str:
        return f"<Commercent(id={self.id}, Num_reg='{self.Num_reg}', Nom_com='{self.Nom_com}')>"
    
    @property
    def full_name(self) -> str:
        """Get full name of the commercial entity"""
        return f"{self.Nom_com} {self.Prenom_com}"


class Activite(Base):
    """Activity model"""
    __tablename__ = 'activite'

    id = Column(Integer, primary_key=True)
    Code_act = Column(String(20), nullable=False)
    Nom_act = Column(String(50), nullable=False)
    parent_id = Column(Integer, ForeignKey("commercent.id"), nullable=False)

    # Relationships
    commercent = relationship("Commercent", back_populates="activities")
    
    def __init__(self, Code_act: str, Nom_act: str, parent_id: int):
        self.Code_act = Code_act
        self.Nom_act = Nom_act
        self.parent_id = parent_id
    
    def __repr__(self) -> str:
        return f"<Activite(id={self.id}, Code_act='{self.Code_act}', Nom_act='{self.Nom_act}')>"


class Activite_Tab(Base):
    """Activity template model"""
    __tablename__ = 'activite_tab'

    id = Column(Integer, primary_key=True)
    Nom_act = Column(String(50), nullable=False, unique=True)
    
    def __init__(self, Nom_act: str):
        self.Nom_act = Nom_act
    
    def __repr__(self) -> str:
        return f"<Activite_Tab(id={self.id}, Nom_act='{self.Nom_act}')>"


class Infraction(Base):
    """Infraction model"""
    __tablename__ = 'infraction'

    id = Column(Integer, primary_key=True)
    Nom_inf = Column(String(50), nullable=False, unique=True)
    
    def __init__(self, Nom_inf: str):
        self.Nom_inf = Nom_inf
    
    def __repr__(self) -> str:
        return f"<Infraction(id={self.id}, Nom_inf='{self.Nom_inf}')>"


class Pv(Base):
    """Process verbal (PV) model"""
    __tablename__ = 'pv'

    id = Column(Integer, primary_key=True)
    nume_pv = Column(Integer, nullable=False)
    date_pv = Column(Date)
    date_constatation = Column(Date)
    date_envoi_jus = Column(Date)
    date_audience = Column(Date)
    annee = Column(Integer)
    infraction = Column(String(50), nullable=False)
    parent_id = Column(Integer, ForeignKey("commercent.id"), nullable=False)

    # Relationships
    commercent = relationship("Commercent", back_populates="pvs")
    avertissements = relationship("Avertisment", back_populates="pv", cascade="all, delete-orphan")
    
    def __init__(self, nume_pv: int, date_pv: Optional[date] = None,
                 date_constatation: Optional[date] = None, date_envoi_jus: Optional[date] = None,
                 date_audience: Optional[date] = None, annee: Optional[int] = None,
                 infraction: str = "", parent_id: int = 0):
        self.nume_pv = nume_pv
        self.date_pv = date_pv
        self.date_constatation = date_constatation
        self.date_envoi_jus = date_envoi_jus
        self.date_audience = date_audience
        self.annee = annee
        self.infraction = infraction
        self.parent_id = parent_id
    
    def __repr__(self) -> str:
        return f"<Pv(id={self.id}, nume_pv={self.nume_pv}, infraction='{self.infraction}')>"


class Avertisment(Base):
    """Warning/Fine model"""
    __tablename__ = "avertissement"

    id = Column(Integer, primary_key=True)
    date_avert = Column(Date)
    nume_recu = Column(Integer, nullable=False)
    date_recu = Column(Date)
    montant = Column(Float, nullable=False, default=0.0)
    parent_id = Column(Integer, ForeignKey("pv.id"), nullable=False)

    # Relationships
    pv = relationship("Pv", back_populates="avertissements")
    
    def __init__(self, date_avert: Optional[date] = None, nume_recu: int = 0,
                 date_recu: Optional[date] = None, montant: float = 0.0,
                 parent_id: int = 0):
        self.date_avert = date_avert
        self.nume_recu = nume_recu
        self.date_recu = date_recu
        self.montant = montant
        self.parent_id = parent_id
    
    def __repr__(self) -> str:
        return f"<Avertisment(id={self.id}, nume_recu={self.nume_recu}, montant={self.montant})>"

# SCTX Management System v2.0

A refactored and improved version of the SCTX commercial activities and infractions management system built with PySide6 and SQLAlchemy.

## 🚀 What's New in v2.0

### Major Improvements

1. **Clean Architecture**: Implemented proper separation of concerns with distinct layers:
   - **Database Layer**: Centralized database management with connection pooling
   - **Service Layer**: Business logic and data operations
   - **UI Layer**: Modular, reusable UI components
   - **Models**: Clean SQLAlchemy models with proper relationships

2. **Performance Enhancements**:
   - Database connection pooling and session management
   - Lazy loading for large datasets
   - Optimized queries with proper indexing
   - Caching mechanisms for frequently accessed data

3. **Code Quality**:
   - Type hints throughout the codebase
   - Comprehensive error handling and logging
   - Input validation with custom validators
   - Modular, testable components

4. **User Experience**:
   - Improved error messages in Arabic and English
   - Auto-save functionality
   - Better form validation with real-time feedback
   - Responsive UI with proper loading states

## 📁 Project Structure

```
sctx/
├── database/
│   ├── __init__.py
│   └── database_manager.py      # Database connection and session management
├── models/
│   ├── __init__.py
│   └── models.py               # SQLAlchemy models
├── services/
│   ├── __init__.py
│   └── data_service.py         # Business logic and data operations
├── ui/
│   ├── __init__.py
│   └── components/
│       ├── __init__.py
│       ├── activity_widget.py  # Reusable activity management widget
│       └── infraction_widget.py # Reusable infraction management widget
├── utils/
│   ├── __init__.py
│   ├── validators.py           # Input validation utilities
│   └── logger.py              # Logging configuration
├── data/
│   └── sctx.db                # SQLite database
├── logs/                      # Application logs
├── config.py                  # Configuration management
├── main.py                    # Refactored main application
├── requirements.txt           # Python dependencies
├── test_new_architecture.py   # Architecture validation tests
└── README.md                  # This file
```

## 🛠 Installation

1. **Clone the repository** (or use existing files):
   ```bash
   cd /path/to/sctx/project
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Test the new architecture**:
   ```bash
   python test_new_architecture.py
   ```

4. **Run the application**:
   ```bash
   python main.py
   ```

## 🔧 Configuration

The application uses a flexible configuration system. You can customize settings by:

1. **Environment Variables**:
   ```bash
   export SCTX_DB_URL="sqlite:///custom/path/sctx.db"
   export SCTX_LOG_LEVEL="DEBUG"
   export SCTX_WINDOW_TITLE="Custom Title"
   ```

2. **Configuration File** (`config.json`):
   ```json
   {
     "database": {
       "url": "sqlite:///data/sctx.db",
       "echo": false
     },
     "ui": {
       "window_title": "SCTX Management System v2.0",
       "theme": "default"
     },
     "logging": {
       "level": "INFO"
     }
   }
   ```

## 📊 Key Features

### Database Management
- **Connection Pooling**: Efficient database connection management
- **Session Management**: Automatic session cleanup and error handling
- **Migration Support**: Easy database schema updates
- **Backup System**: Automated database backups

### Data Validation
- **Input Validation**: Comprehensive validation for all user inputs
- **Arabic Text Support**: Proper handling of Arabic names and addresses
- **Date Validation**: Smart date parsing and validation
- **Business Rules**: Enforcement of business logic constraints

### User Interface
- **Modular Components**: Reusable UI widgets for different entities
- **Responsive Design**: Adaptive layout for different screen sizes
- **Error Handling**: User-friendly error messages
- **Auto-save**: Automatic saving of user work

### Logging and Monitoring
- **Structured Logging**: Comprehensive logging with different levels
- **Performance Monitoring**: Track slow operations and bottlenecks
- **Error Tracking**: Detailed error logs for debugging
- **Audit Trail**: Track all data modifications

## 🧪 Testing

Run the architecture validation tests:

```bash
python test_new_architecture.py
```

This will verify:
- All modules can be imported correctly
- Database connections work properly
- Data services function correctly
- Input validators work as expected
- Configuration loads successfully

## 🔄 Migration from v1.0

The new architecture is designed to be backward compatible. Your existing database will work with the new system. However, to take full advantage of the improvements:

1. **Backup your data**:
   ```bash
   cp data/sctx.db data/sctx_backup.db
   ```

2. **Run the new application**:
   ```bash
   python main.py
   ```

3. **Verify data integrity** using the built-in validation tools

## 🐛 Troubleshooting

### Common Issues

1. **Import Errors**:
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python path includes the project directory

2. **Database Errors**:
   - Verify database file permissions
   - Check available disk space
   - Review database logs in `logs/` directory

3. **UI Issues**:
   - Ensure PySide6 is properly installed
   - Check display settings and scaling
   - Review UI logs for specific errors

### Getting Help

1. Check the logs in the `logs/` directory
2. Run the test script to identify issues
3. Review error messages for specific guidance

## 🚀 Performance Tips

1. **Database Optimization**:
   - Use pagination for large datasets
   - Enable database indexing for frequently searched fields
   - Regular database maintenance and cleanup

2. **UI Responsiveness**:
   - Use lazy loading for large tables
   - Implement search delays to avoid excessive queries
   - Cache frequently accessed data

3. **Memory Management**:
   - Monitor memory usage with large datasets
   - Use database sessions efficiently
   - Clear unused objects regularly

## 🔮 Future Enhancements

- **Multi-language Support**: Full internationalization
- **Advanced Reporting**: Enhanced PDF and Excel export
- **Cloud Integration**: Support for cloud databases
- **Mobile App**: Companion mobile application
- **API Integration**: REST API for external integrations
- **Advanced Analytics**: Business intelligence features

## 📝 License

This project is licensed under the MIT License - see the original project documentation for details.

## 👨‍💻 Developer

**Original Developer**: boumares abdelhafid  
**Refactored Version**: Enhanced with modern architecture and best practices

---

*This refactored version maintains all original functionality while providing a much cleaner, more maintainable, and performant codebase.*

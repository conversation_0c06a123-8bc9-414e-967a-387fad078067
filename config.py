"""
Configuration settings for SCTX Application
"""
import os
from pathlib import Path
from dataclasses import dataclass
from typing import Optional


@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str = "sqlite:///data/sctx.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10
    pool_timeout: int = 30


@dataclass
class UIConfig:
    """UI configuration"""
    window_title: str = "SCTX Management System v2.0"
    window_width: int = 1200
    window_height: int = 800
    theme: str = "default"
    language: str = "ar"  # Arabic
    font_family: str = "Arial"
    font_size: int = 10


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    log_dir: str = "logs"
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


@dataclass
class AppConfig:
    """Main application configuration"""
    database: DatabaseConfig
    ui: UIConfig
    logging: LoggingConfig
    
    # Application settings
    auto_save_interval: int = 300000  # 5 minutes in milliseconds
    backup_interval: int = 86400  # 24 hours in seconds
    max_recent_files: int = 10
    
    # Paths
    data_dir: str = "data"
    backup_dir: str = "backups"
    reports_dir: str = "reports"
    temp_dir: str = "temp"
    
    # Performance settings
    table_page_size: int = 100
    search_delay: int = 500  # milliseconds
    cache_size: int = 1000
    
    # Security settings
    session_timeout: int = 3600  # 1 hour in seconds
    max_login_attempts: int = 3
    
    def __post_init__(self):
        """Create directories if they don't exist"""
        for dir_path in [self.data_dir, self.backup_dir, self.reports_dir, 
                        self.temp_dir, self.logging.log_dir]:
            Path(dir_path).mkdir(exist_ok=True)


def load_config(config_file: Optional[str] = None) -> AppConfig:
    """
    Load configuration from file or environment variables
    
    Args:
        config_file: Path to configuration file (optional)
        
    Returns:
        AppConfig instance
    """
    # Default configuration
    database_config = DatabaseConfig()
    ui_config = UIConfig()
    logging_config = LoggingConfig()
    
    # Override with environment variables if present
    if os.getenv("SCTX_DB_URL"):
        database_config.url = os.getenv("SCTX_DB_URL")
    
    if os.getenv("SCTX_DB_ECHO"):
        database_config.echo = os.getenv("SCTX_DB_ECHO").lower() == "true"
    
    if os.getenv("SCTX_LOG_LEVEL"):
        logging_config.level = os.getenv("SCTX_LOG_LEVEL")
    
    if os.getenv("SCTX_WINDOW_TITLE"):
        ui_config.window_title = os.getenv("SCTX_WINDOW_TITLE")
    
    # Create main config
    config = AppConfig(
        database=database_config,
        ui=ui_config,
        logging=logging_config
    )
    
    # Load from file if specified
    if config_file and os.path.exists(config_file):
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                
            # Update config with file values
            if 'database' in file_config:
                for key, value in file_config['database'].items():
                    if hasattr(config.database, key):
                        setattr(config.database, key, value)
                        
            if 'ui' in file_config:
                for key, value in file_config['ui'].items():
                    if hasattr(config.ui, key):
                        setattr(config.ui, key, value)
                        
            if 'logging' in file_config:
                for key, value in file_config['logging'].items():
                    if hasattr(config.logging, key):
                        setattr(config.logging, key, value)
                        
            # Update main config
            for key, value in file_config.items():
                if key not in ['database', 'ui', 'logging'] and hasattr(config, key):
                    setattr(config, key, value)
                    
        except Exception as e:
            print(f"Warning: Failed to load config file {config_file}: {e}")
    
    return config


def save_config(config: AppConfig, config_file: str):
    """
    Save configuration to file
    
    Args:
        config: AppConfig instance
        config_file: Path to save configuration
    """
    try:
        import json
        from dataclasses import asdict
        
        config_dict = asdict(config)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
    except Exception as e:
        print(f"Error: Failed to save config file {config_file}: {e}")


# Default configuration instance
default_config = load_config()


# Configuration file paths
CONFIG_FILE = "config.json"
USER_CONFIG_FILE = os.path.expanduser("~/.sctx/config.json")


def get_config() -> AppConfig:
    """Get the application configuration"""
    # Try to load user config first, then system config, then default
    for config_path in [USER_CONFIG_FILE, CONFIG_FILE]:
        if os.path.exists(config_path):
            return load_config(config_path)
    
    return default_config

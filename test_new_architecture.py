#!/usr/bin/env python3
"""
Test script for the new SCTX application architecture
"""
import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test that all new modules can be imported"""
    print("Testing imports...")
    
    try:
        from database.database_manager import DatabaseManager
        print("✓ DatabaseManager imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import DatabaseManager: {e}")
        return False
    
    try:
        from models.models import Commercent, Activite, Infraction, Pv, Avertisment
        print("✓ Models imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import models: {e}")
        return False
    
    try:
        from services.data_service import DataService
        print("✓ DataService imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import DataService: {e}")
        return False
    
    try:
        from utils.validators import InputValidator
        print("✓ InputValidator imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import InputValidator: {e}")
        return False
    
    try:
        from utils.logger import setup_logger
        print("✓ Logger utilities imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import logger utilities: {e}")
        return False
    
    try:
        from config import get_config
        print("✓ Configuration imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import configuration: {e}")
        return False
    
    return True


def test_database_connection():
    """Test database connection and basic operations"""
    print("\nTesting database connection...")
    
    try:
        from database.database_manager import DatabaseManager
        
        # Create a test database
        db_manager = DatabaseManager("sqlite:///test_sctx.db")
        
        # Test session creation
        with db_manager.get_session() as session:
            print("✓ Database session created successfully")
        
        # Test table count
        count = db_manager.get_table_count('commercent')
        print(f"✓ Commercial entities count: {count}")
        
        # Clean up
        db_manager.close()
        
        # Remove test database
        test_db_path = Path("test_sctx.db")
        if test_db_path.exists():
            test_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False


def test_data_service():
    """Test data service operations"""
    print("\nTesting data service...")
    
    try:
        from database.database_manager import DatabaseManager
        from services.data_service import DataService
        
        # Create test database and service
        db_manager = DatabaseManager("sqlite:///test_sctx.db")
        data_service = DataService(db_manager)
        
        # Test getting statistics
        stats = data_service.get_statistics()
        print(f"✓ Statistics retrieved: {stats}")
        
        # Test getting all commercents
        commercents = data_service.get_all_commercents(limit=10)
        print(f"✓ Retrieved {len(commercents)} commercial entities")
        
        # Test getting activity templates
        templates = data_service.get_all_activity_templates()
        print(f"✓ Retrieved {len(templates)} activity templates")
        
        # Test getting infractions
        infractions = data_service.get_all_infractions()
        print(f"✓ Retrieved {len(infractions)} infractions")
        
        # Clean up
        db_manager.close()
        
        # Remove test database
        test_db_path = Path("test_sctx.db")
        if test_db_path.exists():
            test_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Data service test failed: {e}")
        return False


def test_validators():
    """Test input validators"""
    print("\nTesting validators...")
    
    try:
        from utils.validators import InputValidator, ValidationError
        
        # Test valid inputs
        reg_num = InputValidator.validate_registration_number("REG-123")
        print(f"✓ Valid registration number: {reg_num}")
        
        name = InputValidator.validate_name("John Doe", "Name")
        print(f"✓ Valid name: {name}")
        
        # Test invalid inputs
        try:
            InputValidator.validate_registration_number("")
            print("✗ Should have failed for empty registration number")
            return False
        except ValidationError:
            print("✓ Correctly rejected empty registration number")
        
        try:
            InputValidator.validate_name("", "Name")
            print("✗ Should have failed for empty name")
            return False
        except ValidationError:
            print("✓ Correctly rejected empty name")
        
        return True
        
    except Exception as e:
        print(f"✗ Validator test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from config import get_config
        
        config = get_config()
        print(f"✓ Configuration loaded successfully")
        print(f"  - Database URL: {config.database.url}")
        print(f"  - Window title: {config.ui.window_title}")
        print(f"  - Log level: {config.logging.level}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("SCTX Application Architecture Test")
    print("=" * 40)
    
    tests = [
        test_imports,
        test_database_connection,
        test_data_service,
        test_validators,
        test_configuration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 40)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The new architecture is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())

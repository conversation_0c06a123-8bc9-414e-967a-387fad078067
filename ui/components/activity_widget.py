"""
Activity Widget Component for SCTX Application
Reusable component for managing activities
"""
import logging
from typing import Optional, List, Dict, Any
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QLineEdit, QTableWidget, QTableWidgetItem, 
                               QMessageBox, QLabel, QComboBox)
from PySide6.QtCore import Signal, Qt

from services.data_service import DataService
from models.models import Activite, Activite_Tab
from utils.validators import InputValidator, ValidationError
from utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class ActivityWidget(QWidget, LoggerMixin):
    """Reusable widget for managing activities"""
    
    # Signals
    activity_selected = Signal(int)  # Emitted when an activity is selected
    activity_changed = Signal()     # Emitted when activities are modified
    
    def __init__(self, data_service: DataService, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.current_activity_id: Optional[int] = None
        self.current_commercent_id: Optional[int] = None
        self.activities: List[Activite] = []
        self.activity_templates: List[Activite_Tab] = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_activity_templates()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Activities Management")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Input section
        input_layout = QHBoxLayout()
        
        # Activity code input
        self.code_input = QLineEdit()
        self.code_input.setPlaceholderText("Activity Code")
        self.code_input.setMaxLength(20)
        input_layout.addWidget(QLabel("Code:"))
        input_layout.addWidget(self.code_input)
        
        # Activity name combo box
        self.name_combo = QComboBox()
        self.name_combo.setEditable(True)
        self.name_combo.setPlaceholderText("Select or enter activity name")
        input_layout.addWidget(QLabel("Name:"))
        input_layout.addWidget(self.name_combo)
        
        layout.addLayout(input_layout)
        
        # Buttons section
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add")
        self.add_button.setStyleSheet(self._get_button_style("#4CAF50"))
        buttons_layout.addWidget(self.add_button)
        
        self.update_button = QPushButton("Update")
        self.update_button.setStyleSheet(self._get_button_style("#2196F3"))
        self.update_button.setEnabled(False)
        buttons_layout.addWidget(self.update_button)
        
        self.delete_button = QPushButton("Delete")
        self.delete_button.setStyleSheet(self._get_button_style("#f44336"))
        self.delete_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_button)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.setStyleSheet(self._get_button_style("#9E9E9E"))
        buttons_layout.addWidget(self.clear_button)
        
        self.refresh_button = QPushButton("Refresh Templates")
        self.refresh_button.setStyleSheet(self._get_button_style("#FF9800"))
        buttons_layout.addWidget(self.refresh_button)
        
        layout.addLayout(buttons_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["ID", "Code", "Name"])
        self.table.setColumnHidden(0, True)  # Hide ID column
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        layout.addWidget(self.table)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_activity)
        self.update_button.clicked.connect(self.update_activity)
        self.delete_button.clicked.connect(self.delete_activity)
        self.clear_button.clicked.connect(self.clear_form)
        self.refresh_button.clicked.connect(self.load_activity_templates)
        self.table.currentCellChanged.connect(self.on_table_selection_changed)
        self.code_input.returnPressed.connect(self.add_activity)
        self.name_combo.lineEdit().returnPressed.connect(self.add_activity)
    
    def _get_button_style(self, color: str) -> str:
        """Get button style with specified color"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """
    
    def set_commercent_id(self, commercent_id: int):
        """Set the current commercial entity ID and load activities"""
        self.current_commercent_id = commercent_id
        self.load_activities()
        self.clear_form()
    
    def load_activity_templates(self):
        """Load activity templates into combo box"""
        try:
            self.activity_templates = self.data_service.get_all_activity_templates()
            self.name_combo.clear()
            
            for template in self.activity_templates:
                self.name_combo.addItem(template.Nom_act)
            
            self.status_label.setText(f"Loaded {len(self.activity_templates)} activity templates")
            self.logger.info(f"Loaded {len(self.activity_templates)} activity templates")
            
        except Exception as e:
            self.logger.error(f"Failed to load activity templates: {e}")
            self.show_error("Failed to load activity templates", str(e))
    
    def load_activities(self):
        """Load activities for the current commercial entity"""
        if not self.current_commercent_id:
            self.table.setRowCount(0)
            return
        
        try:
            self.activities = self.data_service.get_activities_by_commercent(self.current_commercent_id)
            self.populate_table()
            self.status_label.setText(f"Loaded {len(self.activities)} activities")
            self.logger.info(f"Loaded {len(self.activities)} activities for commercent {self.current_commercent_id}")
            
        except Exception as e:
            self.logger.error(f"Failed to load activities: {e}")
            self.show_error("Failed to load activities", str(e))
    
    def populate_table(self):
        """Populate the table with activities"""
        self.table.setRowCount(len(self.activities))
        
        for row, activity in enumerate(self.activities):
            self.table.setItem(row, 0, QTableWidgetItem(str(activity.id)))
            self.table.setItem(row, 1, QTableWidgetItem(activity.Code_act or ""))
            self.table.setItem(row, 2, QTableWidgetItem(activity.Nom_act or ""))
        
        # Resize columns to content
        self.table.resizeColumnsToContents()
    
    def on_table_selection_changed(self, current_row: int, current_column: int, 
                                   previous_row: int, previous_column: int):
        """Handle table selection changes"""
        if current_row >= 0 and current_row < len(self.activities):
            activity = self.activities[current_row]
            self.current_activity_id = activity.id
            
            # Populate form
            self.code_input.setText(activity.Code_act or "")
            self.name_combo.setCurrentText(activity.Nom_act or "")
            
            # Enable update/delete buttons
            self.update_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            
            # Emit signal
            self.activity_selected.emit(activity.id)
        else:
            self.current_activity_id = None
            self.update_button.setEnabled(False)
            self.delete_button.setEnabled(False)
    
    def add_activity(self):
        """Add a new activity"""
        if not self.current_commercent_id:
            self.show_warning("No Commercial Entity", "Please select a commercial entity first.")
            return
        
        try:
            # Validate input
            code = InputValidator.validate_activity_code(self.code_input.text())
            name = InputValidator.validate_required_field(self.name_combo.currentText(), "Activity name")
            
            # Create activity
            activity = self.data_service.create_activity(code, name, self.current_commercent_id)
            
            if activity:
                self.load_activities()
                self.clear_form()
                self.status_label.setText("Activity added successfully")
                self.activity_changed.emit()
                self.logger.info(f"Added activity: {code} - {name}")
            else:
                self.show_error("Failed to add activity", "Unknown error occurred")
                
        except ValidationError as e:
            self.show_warning("Validation Error", str(e))
        except Exception as e:
            self.logger.error(f"Failed to add activity: {e}")
            self.show_error("Failed to add activity", str(e))
    
    def update_activity(self):
        """Update the selected activity"""
        if not self.current_activity_id:
            return
        
        try:
            # Validate input
            code = InputValidator.validate_activity_code(self.code_input.text())
            name = InputValidator.validate_required_field(self.name_combo.currentText(), "Activity name")
            
            # Update activity (implementation depends on data service)
            # For now, we'll need to add this method to data service
            success = self.data_service.update_activity(self.current_activity_id, {
                'Code_act': code,
                'Nom_act': name
            })
            
            if success:
                self.load_activities()
                self.clear_form()
                self.status_label.setText("Activity updated successfully")
                self.activity_changed.emit()
                self.logger.info(f"Updated activity ID {self.current_activity_id}")
            else:
                self.show_error("Failed to update activity", "Unknown error occurred")
                
        except ValidationError as e:
            self.show_warning("Validation Error", str(e))
        except Exception as e:
            self.logger.error(f"Failed to update activity: {e}")
            self.show_error("Failed to update activity", str(e))
    
    def delete_activity(self):
        """Delete the selected activity"""
        if not self.current_activity_id:
            return
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            "Are you sure you want to delete this activity?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success = self.data_service.delete_activity(self.current_activity_id)
                
                if success:
                    self.load_activities()
                    self.clear_form()
                    self.status_label.setText("Activity deleted successfully")
                    self.activity_changed.emit()
                    self.logger.info(f"Deleted activity ID {self.current_activity_id}")
                else:
                    self.show_error("Failed to delete activity", "Unknown error occurred")
                    
            except Exception as e:
                self.logger.error(f"Failed to delete activity: {e}")
                self.show_error("Failed to delete activity", str(e))
    
    def clear_form(self):
        """Clear the form"""
        self.code_input.clear()
        self.name_combo.setCurrentText("")
        self.current_activity_id = None
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.table.clearSelection()
    
    def show_error(self, title: str, message: str):
        """Show error message"""
        QMessageBox.critical(self, title, message)
    
    def show_warning(self, title: str, message: str):
        """Show warning message"""
        QMessageBox.warning(self, title, message)
    
    def show_info(self, title: str, message: str):
        """Show info message"""
        QMessageBox.information(self, title, message)

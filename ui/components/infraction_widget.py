"""
Infraction Widget Component for SCTX Application
Reusable component for managing infractions
"""
import logging
from typing import Optional, List
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QLineEdit, QTableWidget, QTableWidgetItem, 
                               QMessageBox, QLabel)
from PySide6.QtCore import Signal, Qt

from services.data_service import DataService
from models.models import Infraction
from utils.validators import InputValidator, ValidationError
from utils.logger import LoggerMixin

logger = logging.getLogger(__name__)


class InfractionWidget(QWidget, LoggerMixin):
    """Reusable widget for managing infractions"""
    
    # Signals
    infraction_selected = Signal(int)  # Emitted when an infraction is selected
    infraction_changed = Signal()      # Emitted when infractions are modified
    
    def __init__(self, data_service: DataService, parent=None):
        super().__init__(parent)
        self.data_service = data_service
        self.current_infraction_id: Optional[int] = None
        self.infractions: List[Infraction] = []
        
        self.setup_ui()
        self.setup_connections()
        self.load_infractions()
    
    def setup_ui(self):
        """Setup the user interface"""
        layout = QVBoxLayout(self)
        
        # Title
        title_label = QLabel("Infractions Management")
        title_label.setStyleSheet("font-weight: bold; font-size: 14px; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # Input section
        input_layout = QHBoxLayout()
        
        # Infraction name input
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("Enter infraction name")
        self.name_input.setMaxLength(50)
        input_layout.addWidget(QLabel("Name:"))
        input_layout.addWidget(self.name_input)
        
        layout.addLayout(input_layout)
        
        # Search section
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search infractions...")
        search_layout.addWidget(QLabel("Search:"))
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # Buttons section
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("Add")
        self.add_button.setStyleSheet(self._get_button_style("#4CAF50"))
        buttons_layout.addWidget(self.add_button)
        
        self.update_button = QPushButton("Update")
        self.update_button.setStyleSheet(self._get_button_style("#2196F3"))
        self.update_button.setEnabled(False)
        buttons_layout.addWidget(self.update_button)
        
        self.delete_button = QPushButton("Delete")
        self.delete_button.setStyleSheet(self._get_button_style("#f44336"))
        self.delete_button.setEnabled(False)
        buttons_layout.addWidget(self.delete_button)
        
        self.clear_button = QPushButton("Clear")
        self.clear_button.setStyleSheet(self._get_button_style("#9E9E9E"))
        buttons_layout.addWidget(self.clear_button)
        
        self.refresh_button = QPushButton("Refresh")
        self.refresh_button.setStyleSheet(self._get_button_style("#FF9800"))
        buttons_layout.addWidget(self.refresh_button)
        
        layout.addLayout(buttons_layout)
        
        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(2)
        self.table.setHorizontalHeaderLabels(["ID", "Infraction Name"])
        self.table.setColumnHidden(0, True)  # Hide ID column
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setAlternatingRowColors(True)
        layout.addWidget(self.table)
        
        # Status label
        self.status_label = QLabel("Ready")
        self.status_label.setStyleSheet("color: #666; font-style: italic;")
        layout.addWidget(self.status_label)
    
    def setup_connections(self):
        """Setup signal connections"""
        self.add_button.clicked.connect(self.add_infraction)
        self.update_button.clicked.connect(self.update_infraction)
        self.delete_button.clicked.connect(self.delete_infraction)
        self.clear_button.clicked.connect(self.clear_form)
        self.refresh_button.clicked.connect(self.load_infractions)
        self.table.currentCellChanged.connect(self.on_table_selection_changed)
        self.name_input.returnPressed.connect(self.add_infraction)
        self.search_input.textChanged.connect(self.search_infractions)
    
    def _get_button_style(self, color: str) -> str:
        """Get button style with specified color"""
        return f"""
            QPushButton {{
                background-color: {color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {color}dd;
            }}
            QPushButton:pressed {{
                background-color: {color}bb;
            }}
            QPushButton:disabled {{
                background-color: #cccccc;
                color: #666666;
            }}
        """
    
    def load_infractions(self):
        """Load all infractions"""
        try:
            self.infractions = self.data_service.get_all_infractions()
            self.populate_table()
            self.status_label.setText(f"Loaded {len(self.infractions)} infractions")
            self.logger.info(f"Loaded {len(self.infractions)} infractions")
            
        except Exception as e:
            self.logger.error(f"Failed to load infractions: {e}")
            self.show_error("Failed to load infractions", str(e))
    
    def search_infractions(self):
        """Search infractions based on search input"""
        search_term = self.search_input.text().strip()
        
        if not search_term:
            self.load_infractions()
            return
        
        try:
            self.infractions = self.data_service.search_infractions(search_term)
            self.populate_table()
            self.status_label.setText(f"Found {len(self.infractions)} infractions matching '{search_term}'")
            
        except Exception as e:
            self.logger.error(f"Failed to search infractions: {e}")
            self.show_error("Failed to search infractions", str(e))
    
    def populate_table(self):
        """Populate the table with infractions"""
        self.table.setRowCount(len(self.infractions))
        
        for row, infraction in enumerate(self.infractions):
            self.table.setItem(row, 0, QTableWidgetItem(str(infraction.id)))
            self.table.setItem(row, 1, QTableWidgetItem(infraction.Nom_inf or ""))
        
        # Resize columns to content
        self.table.resizeColumnsToContents()
    
    def on_table_selection_changed(self, current_row: int, current_column: int, 
                                   previous_row: int, previous_column: int):
        """Handle table selection changes"""
        if current_row >= 0 and current_row < len(self.infractions):
            infraction = self.infractions[current_row]
            self.current_infraction_id = infraction.id
            
            # Populate form
            self.name_input.setText(infraction.Nom_inf or "")
            
            # Enable update/delete buttons
            self.update_button.setEnabled(True)
            self.delete_button.setEnabled(True)
            
            # Emit signal
            self.infraction_selected.emit(infraction.id)
        else:
            self.current_infraction_id = None
            self.update_button.setEnabled(False)
            self.delete_button.setEnabled(False)
    
    def add_infraction(self):
        """Add a new infraction"""
        try:
            # Validate input
            name = InputValidator.validate_required_field(self.name_input.text(), "Infraction name")
            name = InputValidator.validate_string_length(name, "Infraction name", 2, 50)
            
            # Create infraction
            infraction = self.data_service.create_infraction(name)
            
            if infraction:
                self.load_infractions()
                self.clear_form()
                self.status_label.setText("Infraction added successfully")
                self.infraction_changed.emit()
                self.logger.info(f"Added infraction: {name}")
            else:
                self.show_error("Failed to add infraction", "Unknown error occurred")
                
        except ValidationError as e:
            self.show_warning("Validation Error", str(e))
        except Exception as e:
            self.logger.error(f"Failed to add infraction: {e}")
            self.show_error("Failed to add infraction", str(e))
    
    def update_infraction(self):
        """Update the selected infraction"""
        if not self.current_infraction_id:
            return
        
        try:
            # Validate input
            name = InputValidator.validate_required_field(self.name_input.text(), "Infraction name")
            name = InputValidator.validate_string_length(name, "Infraction name", 2, 50)
            
            # Update infraction
            success = self.data_service.update_infraction(self.current_infraction_id, name)
            
            if success:
                self.load_infractions()
                self.clear_form()
                self.status_label.setText("Infraction updated successfully")
                self.infraction_changed.emit()
                self.logger.info(f"Updated infraction ID {self.current_infraction_id}")
            else:
                self.show_error("Failed to update infraction", "Unknown error occurred")
                
        except ValidationError as e:
            self.show_warning("Validation Error", str(e))
        except Exception as e:
            self.logger.error(f"Failed to update infraction: {e}")
            self.show_error("Failed to update infraction", str(e))
    
    def delete_infraction(self):
        """Delete the selected infraction"""
        if not self.current_infraction_id:
            return
        
        reply = QMessageBox.question(
            self, "Confirm Delete",
            "Are you sure you want to delete this infraction?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                success = self.data_service.delete_infraction(self.current_infraction_id)
                
                if success:
                    self.load_infractions()
                    self.clear_form()
                    self.status_label.setText("Infraction deleted successfully")
                    self.infraction_changed.emit()
                    self.logger.info(f"Deleted infraction ID {self.current_infraction_id}")
                else:
                    self.show_error("Failed to delete infraction", "Unknown error occurred")
                    
            except Exception as e:
                self.logger.error(f"Failed to delete infraction: {e}")
                self.show_error("Failed to delete infraction", str(e))
    
    def clear_form(self):
        """Clear the form"""
        self.name_input.clear()
        self.current_infraction_id = None
        self.update_button.setEnabled(False)
        self.delete_button.setEnabled(False)
        self.table.clearSelection()
    
    def show_error(self, title: str, message: str):
        """Show error message"""
        QMessageBox.critical(self, title, message)
    
    def show_warning(self, title: str, message: str):
        """Show warning message"""
        QMessageBox.warning(self, title, message)
    
    def show_info(self, title: str, message: str):
        """Show info message"""
        QMessageBox.information(self, title, message)

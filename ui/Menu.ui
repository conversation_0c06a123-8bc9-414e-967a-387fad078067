<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>842</width>
    <height>737</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="sizeIncrement">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="baseSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="font">
   <font>
    <family>Tahoma</family>
    <pointsize>9</pointsize>
   </font>
  </property>
  <property name="windowTitle">
   <string>تطبيق أرشيف المنازعات</string>
  </property>
  <property name="windowIcon">
   <iconset resource="../images.qrc">
    <normaloff>:/img/img/siege.jpg</normaloff>:/img/img/siege.jpg</iconset>
  </property>
  <property name="autoFillBackground">
   <bool>true</bool>
  </property>
  <property name="toolButtonStyle">
   <enum>Qt::ToolButtonTextUnderIcon</enum>
  </property>
  <property name="tabShape">
   <enum>QTabWidget::Rounded</enum>
  </property>
  <property name="dockOptions">
   <set>QMainWindow::AllowTabbedDocks|QMainWindow::AnimatedDocks</set>
  </property>
  <property name="unifiedTitleAndToolBarOnMac">
   <bool>false</bool>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QTabWidget" name="tabWidget_2">
      <property name="styleSheet">
       <string notr="true">background-color: qlineargradient(spread:reflect, x1:0.629, y1:0.392045, x2:0, y2:1, stop:0.696682 rgba(44, 64, 174, 255), stop:0.981043 rgba(255, 255, 255, 255));</string>
      </property>
      <property name="tabPosition">
       <enum>QTabWidget::West</enum>
      </property>
      <widget class="QWidget" name="ajou_com">
       <attribute name="title">
        <string>اضافة تاجر</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_3">
        <item row="0" column="0">
         <widget class="QFrame" name="frame_2">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <property name="spacing">
              <number>13</number>
             </property>
             <item>
              <widget class="QLineEdit" name="reg_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>رقم السجل</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="nom_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>لقب التاجر</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="prenom_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>اسم التاجر</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(0, 0, 0);
font: 700 12pt &quot;Segoe UI&quot;;</string>
               </property>
               <property name="text">
                <string>تاريخ الميلاد :</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDateEdit" name="date_nai_com">
               <property name="styleSheet">
                <string notr="true">font: 700 11pt &quot;Segoe UI&quot;;
background-color: rgb(255, 255, 255);</string>
               </property>
               <property name="buttonSymbols">
                <enum>QAbstractSpinBox::UpDownArrows</enum>
               </property>
               <property name="calendarPopup">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="lieu_nai_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>مكان الميلاذ</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="prenomPer_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>اسم الأب</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="nomMe_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>لقب الأم</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="prenomMer_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>اسم الأم</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="Adresse_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>العنوان</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLineEdit" name="adresseLoc_com">
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>38</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
               <property name="placeholderText">
                <string>عنوان المحل</string>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QLabel" name="label_4">
               <property name="maximumSize">
                <size>
                 <width>381</width>
                 <height>30</height>
                </size>
               </property>
               <property name="layoutDirection">
                <enum>Qt::LeftToRight</enum>
               </property>
               <property name="styleSheet">
                <string notr="true">color: rgb(0, 0, 0);
font: 700 12pt &quot;Segoe UI&quot;;</string>
               </property>
               <property name="text">
                <string>تاريخ السجل :</string>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QDateEdit" name="dateReg_com">
               <property name="styleSheet">
                <string notr="true">font: 700 11pt &quot;Segoe UI&quot;;
background-color: rgb(255, 255, 255);</string>
               </property>
               <property name="correctionMode">
                <enum>QAbstractSpinBox::CorrectToPreviousValue</enum>
               </property>
               <property name="calendarPopup">
                <bool>true</bool>
               </property>
              </widget>
             </item>
             <item>
              <widget class="QComboBox" name="setuation_com">
               <property name="styleSheet">
                <string notr="true">font: 700 10pt &quot;Segoe UI&quot;;</string>
               </property>
               <item>
                <property name="text">
                 <string>حاضر</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>غائب</string>
                </property>
               </item>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="ajou_act">
       <attribute name="title">
        <string>اضافة نشاط</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_2">
        <item row="0" column="0">
         <widget class="QFrame" name="frame">
          <property name="frameShape">
           <enum>QFrame::HLine</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QGridLayout" name="gridLayout">
           <property name="leftMargin">
            <number>2</number>
           </property>
           <property name="topMargin">
            <number>2</number>
           </property>
           <property name="rightMargin">
            <number>2</number>
           </property>
           <property name="bottomMargin">
            <number>2</number>
           </property>
           <property name="horizontalSpacing">
            <number>6</number>
           </property>
           <item row="0" column="0">
            <widget class="QLineEdit" name="nom_activete">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>31</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="placeholderText">
              <string>اسم النشاط</string>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="QLineEdit" name="code_actevite">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>31</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="placeholderText">
              <string>رمز النشاط</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="ajou_inf">
       <attribute name="title">
        <string>اضافة مخالفة</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout_7">
        <item>
         <widget class="QFrame" name="frame_3">
          <property name="frameShape">
           <enum>QFrame::HLine</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_4">
           <item>
            <widget class="QLineEdit" name="nom_infraction">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>31</height>
              </size>
             </property>
             <property name="styleSheet">
              <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;
color: rgb(255, 255, 255);
</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
             </property>
             <property name="placeholderText">
              <string>اسم المخالفة</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
    <item>
     <widget class="QTabWidget" name="tabWidget">
      <property name="font">
       <font>
        <family>Segoe UI</family>
        <pointsize>12</pointsize>
        <bold>true</bold>
       </font>
      </property>
      <property name="layoutDirection">
       <enum>Qt::RightToLeft</enum>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: qlineargradient(spread:pad, x1:0.426136, y1:0.159091, x2:1, y2:1, stop:0.142045 rgba(58, 196, 255, 255), stop:1 rgba(255, 255, 255, 255));</string>
      </property>
      <property name="tabPosition">
       <enum>QTabWidget::North</enum>
      </property>
      <property name="tabShape">
       <enum>QTabWidget::Triangular</enum>
      </property>
      <property name="currentIndex">
       <number>0</number>
      </property>
      <property name="elideMode">
       <enum>Qt::ElideLeft</enum>
      </property>
      <property name="usesScrollButtons">
       <bool>true</bool>
      </property>
      <property name="documentMode">
       <bool>true</bool>
      </property>
      <property name="tabsClosable">
       <bool>true</bool>
      </property>
      <property name="movable">
       <bool>true</bool>
      </property>
      <property name="tabBarAutoHide">
       <bool>true</bool>
      </property>
      <widget class="QWidget" name="Aceule">
       <attribute name="icon">
        <iconset resource="../images.qrc">
         <normaloff>:/img/img/home_automation_50px.png</normaloff>
         <selectedon>:/img/img/house.png</selectedon>:/img/img/home_automation_50px.png</iconset>
       </attribute>
       <attribute name="title">
        <string>الرئيسية</string>
       </attribute>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QFrame" name="frame_4">
          <property name="frameShape">
           <enum>QFrame::StyledPanel</enum>
          </property>
          <property name="frameShadow">
           <enum>QFrame::Raised</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_2">
           <item>
            <widget class="QLabel" name="label_5">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>40</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>14</pointsize>
               <italic>true</italic>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>الجمهورية الجزائرية الديمقراطية الشعبية</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_6">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>40</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>14</pointsize>
               <italic>true</italic>
               <bold>true</bold>
              </font>
             </property>
             <property name="text">
              <string>وزارة التجارة و ترقية الصادرات</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_2">
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>40</height>
              </size>
             </property>
             <property name="font">
              <font>
               <pointsize>14</pointsize>
               <italic>true</italic>
               <bold>true</bold>
              </font>
             </property>
             <property name="layoutDirection">
              <enum>Qt::RightToLeft</enum>
             </property>
             <property name="autoFillBackground">
              <bool>false</bool>
             </property>
             <property name="text">
              <string>المديرية الولائية للتجارة و ترقية الصادرات بشار</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLabel" name="label_3">
             <property name="styleSheet">
              <string notr="true">image: url(:/img/img/siege.jpg);</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="commercent">
       <attribute name="icon">
        <iconset>
         <selectedon>:/img/img/icons8_add_user_male.ico</selectedon>
        </iconset>
       </attribute>
       <attribute name="title">
        <string>التجار</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_4">
        <item row="2" column="0">
         <widget class="QComboBox" name="combox_com">
          <property name="styleSheet">
           <string notr="true">font: 700 10pt &quot;Segoe UI&quot;;
</string>
          </property>
          <item>
           <property name="text">
            <string>لقب التاجر</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>اسم التاجر</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>السجل التجاري</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QLineEdit" name="reche_com">
          <property name="styleSheet">
           <string notr="true">font: 700 12pt &quot;Segoe UI&quot;;</string>
          </property>
          <property name="placeholderText">
           <string>بحث سريع</string>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QTableWidget" name="tableWidget_com">
          <property name="enabled">
           <bool>true</bool>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <column>
           <property name="text">
            <string>id</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>رقم السجل</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>لقب التاجر</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم التاجر</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>تاريخ الميلاذ</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>مكان الميلاذ</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم الأب</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>لقب الأم</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم الأم</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>العنوان</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>عنوان المحل</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>تاريخ السجل</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>الحالة</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="actevite">
       <attribute name="icon">
        <iconset resource="../images.qrc">
         <normaloff>:/img/img/icons8_device_shop_32.png</normaloff>
         <selectedon>:/img/img/icons8_chevron_right_32.png</selectedon>:/img/img/icons8_device_shop_32.png</iconset>
       </attribute>
       <attribute name="title">
        <string>الانشطة </string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_6">
        <item row="0" column="0">
         <widget class="QTableWidget" name="tableWidget_act">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <column>
           <property name="text">
            <string>id</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>رمز النشاط</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم اانشاط</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
           <property name="icon">
            <iconset resource="../images.qrc">
             <normaloff>:/img/img/house.png</normaloff>:/img/img/house.png</iconset>
           </property>
          </column>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QTableWidget" name="tableWidget_com2">
          <property name="autoScrollMargin">
           <number>16</number>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <column>
           <property name="text">
            <string>id</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>رقم السجل</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>لقب التاجر</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم التاجر</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="QLineEdit" name="rech_act">
          <property name="placeholderText">
           <string>بحث سريع</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
      <widget class="QWidget" name="infraction">
       <attribute name="icon">
        <iconset>
         <selectedon>:/img/img/icons8_briefcase_32.png</selectedon>
        </iconset>
       </attribute>
       <attribute name="title">
        <string>المخالفات</string>
       </attribute>
       <layout class="QGridLayout" name="gridLayout_7">
        <item row="0" column="0">
         <widget class="QTableWidget" name="tableWidget_inf">
          <property name="font">
           <font>
            <pointsize>12</pointsize>
           </font>
          </property>
          <property name="autoScrollMargin">
           <number>16</number>
          </property>
          <property name="selectionBehavior">
           <enum>QAbstractItemView::SelectRows</enum>
          </property>
          <column>
           <property name="text">
            <string>ID</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
            </font>
           </property>
          </column>
          <column>
           <property name="text">
            <string>اسم المخالفة</string>
           </property>
           <property name="font">
            <font>
             <pointsize>10</pointsize>
             <bold>false</bold>
            </font>
           </property>
           <property name="textAlignment">
            <set>AlignJustify|AlignVCenter</set>
           </property>
           <property name="foreground">
            <brush brushstyle="SolidPattern">
             <color alpha="255">
              <red>0</red>
              <green>0</green>
              <blue>0</blue>
             </color>
            </brush>
           </property>
           <property name="icon">
            <iconset resource="../images.qrc">
             <normaloff>:/img/img/add.png</normaloff>:/img/img/add.png</iconset>
           </property>
          </column>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLineEdit" name="rech_inf">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <property name="font">
           <font>
            <pointsize>12</pointsize>
            <bold>true</bold>
           </font>
          </property>
          <property name="styleSheet">
           <string notr="true">f</string>
          </property>
          <property name="placeholderText">
           <string>بحث سريع</string>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menuBar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>842</width>
     <height>22</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <family>Tahoma</family>
     <pointsize>10</pointsize>
    </font>
   </property>
   <widget class="QMenu" name="menuView">
    <property name="title">
     <string>View</string>
    </property>
    <addaction name="separator"/>
    <addaction name="actioncommercent"/>
    <addaction name="separator"/>
    <addaction name="actionactevite"/>
    <addaction name="separator"/>
    <addaction name="actioninfraction"/>
   </widget>
   <widget class="QMenu" name="menuApropos">
    <property name="title">
     <string>Apropos</string>
    </property>
    <addaction name="actionaprops"/>
   </widget>
   <addaction name="menuView"/>
   <addaction name="menuApropos"/>
  </widget>
  <widget class="QToolBar" name="toolBar">
   <property name="windowTitle">
    <string>toolBar</string>
   </property>
   <attribute name="toolBarArea">
    <enum>LeftToolBarArea</enum>
   </attribute>
   <attribute name="toolBarBreak">
    <bool>false</bool>
   </attribute>
   <addaction name="actionNouveau"/>
   <addaction name="separator"/>
   <addaction name="actionAjouter"/>
   <addaction name="separator"/>
   <addaction name="actionModifier"/>
   <addaction name="separator"/>
   <addaction name="actionSuprimer"/>
   <addaction name="separator"/>
   <addaction name="actionImprimer"/>
   <addaction name="separator"/>
   <addaction name="actionSave"/>
   <addaction name="separator"/>
   <addaction name="actionHide"/>
   <addaction name="actionShow"/>
   <addaction name="separator"/>
   <addaction name="actionNext"/>
   <addaction name="actionReturn"/>
   <addaction name="separator"/>
   <addaction name="actionClose"/>
  </widget>
  <action name="actioncommercent">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/icons8_user.ico</normaloff>:/img/img/icons8_user.ico</iconset>
   </property>
   <property name="text">
    <string>commercent</string>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
  </action>
  <action name="actionactevite">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/button.png</normaloff>:/img/img/button.png</iconset>
   </property>
   <property name="text">
    <string>actevite</string>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
  </action>
  <action name="actioninfraction">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/icons8_briefcase_32.png</normaloff>:/img/img/icons8_briefcase_32.png</iconset>
   </property>
   <property name="text">
    <string>infraction</string>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
  </action>
  <action name="actionAjouter">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/trombone.png</normaloff>:/img/img/trombone.png</iconset>
   </property>
   <property name="text">
    <string>Ajouter</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+A</string>
   </property>
  </action>
  <action name="actionModifier">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/liste-de-controle.png</normaloff>:/img/img/liste-de-controle.png</iconset>
   </property>
   <property name="text">
    <string>Modifier</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+M</string>
   </property>
  </action>
  <action name="actionSuprimer">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/ciseaux.png</normaloff>:/img/img/ciseaux.png</iconset>
   </property>
   <property name="text">
    <string>Suprimer</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionImprimer">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/dechiqueteuse.png</normaloff>:/img/img/dechiqueteuse.png</iconset>
   </property>
   <property name="text">
    <string>Imprimer</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+P</string>
   </property>
  </action>
  <action name="actionClose">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/power-button.png</normaloff>:/img/img/power-button.png</iconset>
   </property>
   <property name="text">
    <string>Close</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+F</string>
   </property>
  </action>
  <action name="actionSave">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/binder_64px.png</normaloff>:/img/img/binder_64px.png</iconset>
   </property>
   <property name="text">
    <string>Save</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionNouveau">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/plus.png</normaloff>:/img/img/plus.png</iconset>
   </property>
   <property name="text">
    <string>Nouveau</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+N</string>
   </property>
  </action>
  <action name="actionHide">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/double_left_64px.png</normaloff>:/img/img/double_left_64px.png</iconset>
   </property>
   <property name="text">
    <string>Hide</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+H</string>
   </property>
  </action>
  <action name="actionShow">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/double_right_64px.png</normaloff>:/img/img/double_right_64px.png</iconset>
   </property>
   <property name="text">
    <string>Show</string>
   </property>
   <property name="shortcut">
    <string>Ctrl+S</string>
   </property>
  </action>
  <action name="actionaprops">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/button.png</normaloff>:/img/img/button.png</iconset>
   </property>
   <property name="text">
    <string>aprops</string>
   </property>
   <property name="font">
    <font>
     <pointsize>10</pointsize>
    </font>
   </property>
  </action>
  <action name="actionNext">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/icons8-next-page-16.png</normaloff>:/img/img/icons8-next-page-16.png</iconset>
   </property>
   <property name="text">
    <string>Next</string>
   </property>
  </action>
  <action name="actionReturn">
   <property name="icon">
    <iconset resource="../images.qrc">
     <normaloff>:/img/img/icons8-u-turn-to-left-48.png</normaloff>:/img/img/icons8-u-turn-to-left-48.png</iconset>
   </property>
   <property name="text">
    <string>Return</string>
   </property>
  </action>
 </widget>
 <resources>
  <include location="../images.qrc"/>
 </resources>
 <connections>
  <connection>
   <sender>actionAjouter</sender>
   <signal>triggered()</signal>
   <receiver>actionAjouter</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionModifier</sender>
   <signal>triggered()</signal>
   <receiver>actionModifier</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSuprimer</sender>
   <signal>triggered()</signal>
   <receiver>actionSuprimer</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionImprimer</sender>
   <signal>triggered()</signal>
   <receiver>actionImprimer</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionNouveau</sender>
   <signal>triggered()</signal>
   <receiver>actionNouveau</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionSave</sender>
   <signal>triggered()</signal>
   <receiver>actionSave</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionClose</sender>
   <signal>triggered()</signal>
   <receiver>MainWindow</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>499</x>
     <y>331</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionactevite</sender>
   <signal>triggered()</signal>
   <receiver>actionactevite</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actioncommercent</sender>
   <signal>triggered()</signal>
   <receiver>tabWidget</receiver>
   <slot>show()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>763</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actioninfraction</sender>
   <signal>triggered()</signal>
   <receiver>actioninfraction</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tabWidget</sender>
   <signal>tabCloseRequested(int)</signal>
   <receiver>tabWidget</receiver>
   <slot>close()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>763</x>
     <y>342</y>
    </hint>
    <hint type="destinationlabel">
     <x>763</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>reche_com</sender>
   <signal>textChanged(QString)</signal>
   <receiver>reche_com</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>631</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>631</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>rech_act</sender>
   <signal>textChanged(QString)</signal>
   <receiver>rech_act</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>631</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>631</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>rech_inf</sender>
   <signal>textChanged(QString)</signal>
   <receiver>rech_inf</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>631</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>631</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tableWidget_com</sender>
   <signal>currentCellChanged(int,int,int,int)</signal>
   <receiver>tableWidget_com</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>322</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>322</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tableWidget_act</sender>
   <signal>currentCellChanged(int,int,int,int)</signal>
   <receiver>tableWidget_act</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>198</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>198</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tableWidget_com2</sender>
   <signal>currentCellChanged(int,int,int,int)</signal>
   <receiver>tableWidget_com2</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>476</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>476</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tableWidget_inf</sender>
   <signal>currentCellChanged(int,int,int,int)</signal>
   <receiver>tableWidget_inf</receiver>
   <slot>clear()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>561</x>
     <y>337</y>
    </hint>
    <hint type="destinationlabel">
     <x>561</x>
     <y>337</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionaprops</sender>
   <signal>triggered()</signal>
   <receiver>actionaprops</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionShow</sender>
   <signal>triggered()</signal>
   <receiver>tabWidget_2</receiver>
   <slot>show()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>281</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>tabWidget_2</sender>
   <signal>currentChanged(int)</signal>
   <receiver>tabWidget</receiver>
   <slot>setCurrentIndex(int)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>256</x>
     <y>316</y>
    </hint>
    <hint type="destinationlabel">
     <x>650</x>
     <y>316</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionHide</sender>
   <signal>triggered()</signal>
   <receiver>tabWidget_2</receiver>
   <slot>hide()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>281</x>
     <y>342</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionNext</sender>
   <signal>triggered()</signal>
   <receiver>actionNext</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
  <connection>
   <sender>actionReturn</sender>
   <signal>triggered()</signal>
   <receiver>actionReturn</receiver>
   <slot>trigger()</slot>
   <hints>
    <hint type="sourcelabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
    <hint type="destinationlabel">
     <x>-1</x>
     <y>-1</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
